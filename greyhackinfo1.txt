
Tools You Need
Pre-Installed Tools

    whois: You can use this to learn contact details of owner of a server.
    nslookup: If you want to learn IP of a specific domain, use it.
    ScanLan.exe: If you want to see existing computers in a network that you already connected, use it.
    ssh: You can connect to a ssh service if you know user name and password.
    ftp: You can connect to a ftp service if you know user name and password.
    LogViewer.exe: You need this to view and clear logs.
    ping: You can use this to see whether target computer is reachable or not.

Tools from Regular Shop

    nmap: You need this tool to scan open ports of a specific IP.
    smtp-user-list: You need it to view registered accounts on a SMTP server. It only works if SMTP service is running.

Tools from Hack Shop

    AdminMonitor.exe: It warns you when a NPC admin logged in or started trace on you. Always run it at background just in case..
    decipher: You need it to crack passwords.
    scanlib: If you do not want to make your own cusomized hack tools, do not download.
    scanrouter: You need it to scan kernel_router.so. Download it.
    metaxploit.so: All exploits require this file. Keep it up-to-date.
    crypto.so: All crackers require this file. Keep it up-to-date.
    sniffer: You can capture passwords with this tool. Run this on a router you already connected and wait.
    rshell-server and rshell-interface: You have to install rshell-server into your rented server inorder to use rshell-interface. These two tools create a connection between you and victim. You can connect to your victim and do whatever you want on victim's machine once you established a connection. Do Social Engineering to hack NPC with this tools. Check Social Engineering section of this guide.
    If you do not know what to get from Exploits tab, click a service name on right and read it.

Other Official Tools

    ConfigLan: If you want to manage your rented servers or create a network, use this.

Where to Find Shops
Regular / Legal Shops

    Open Browser and type shop or store into search box and hit enter.
    Enter one of those websites.
    You can find nmap, smtp-server-list, http-server, ssh-server, ftp-server and hardwares.
    You can rent servers.


Hack Shops

    You'll recieve a mail that contains hack shop address after finishing tutorial mission.
    You'll recieve a mail that contains hack shop address, if you finished tutorial mission and created a new e-mail account.
    If you did not recieve an IP or address and if you are in online mode, open chat and request a hack shop address from a player.

How to Create E-Mail Account

    Open Browser and type mail or email into search box and hit enter.
    Enter one of those websites.
    Click Email Services
    Fill the empty fields and click Create account.
    As soon as you create an account, you'll recieve a message. It is your tutorial mission. If you finish it, you'll recieve a Hack Shop address.

How to Create Bank Account

    Open Browser and type bank into search box and hit enter.
    Enter one of those websites.
    Click Register
    Fill the empty fields and click Create account.
    You can login your bank account if you click Login tab.

Hack Shop is Empty
It is not empty. You have to click Exploits tab and use pre-generated exploits.
If you still do not know where are tools, click "Old Tools" on right and read it.
If you still do not know what to get from shop, click "Tools You Need" on right and read it.
Where are Hack Tools
Go to hack shop and click Exploits tab. You have to use pre-generated exploits on Exploits page to hack something.
If you still do not know where are hack tools, click "Old Tools" on right and read it.
If you still do not know what to get from shop, click "Tools You Need" on right and read it.
Cracking WiFi Networks

    Open the Terminal enter following command
    airmon start wlan0
    now enter following command to view available wifis
    iwlist wlan0
    Choose one wtih highest power (you can crack it faster)

    Now enter aireplay command like in this picture
    aireplay -b bssid -e essid

    Wait until it gets enough ACKs then hit CTRL+C to stop aireplay.
    (If you do not know how many ACKs you need, click Required ACKs on right and read it.)
    Now enter following command
    aircrack file.cap
    It will tell you the password.
    Now there are two things you can do:
    You can simply click wifi icon on top right, select and connect the wifi that you've cracked...
    or you can enter following command
    iwconfig wlan0 essid bssid password

Required ACKs to Crack WiFi


NOTE: You might need a little more ACKs...
First Mission (Tutorial Mission)
https://www.youtube.com/watch?v=T6zRmfP2uAI
Permission Levels

    Guest: Guest is like a visitor. If you are guest, you can't access critical system files/folders and can only run specific commands. However you can access files inside /home/<USER>/folders.

    Non-Root: A non-root user is a registered user. You can access some system files/folders and run some commands.

    Root: Root is the most important thing. You can do whatever you want in a system. If you are root, you control it. So, if you want to hack something, you definitely have to get root permissions.

apt-get
What is apt-get?
apt-get is a package manager. It allows you to install programs or libraries from command-line without visiting any website.
Official repository is enabled by default.
You can also add hackshop as repository.
You can set up a custom repository too. Click How to Set Up a Server on right and read it for details.
Before you start run this command to update package list and
Always run this command before install, upgrade or search and
Always run after adding a repository.
sudo apt-get update
If you want to update your system or installed packages, run this command:
sudo apt-get upgrade
Run this command to install something:
sudo apt-get install
Example
sudo apt-get install init.so
Run this command to search programs:
sudo apt-get search
Example:
sudo apt-get search kernel
Run this command to see available packages in a repository:
sudo apt-get show
Example
sudo apt-get show 127.0.0.1
Run this command to add a new repository
sudo apt-get addrepo
Example
sudo apt-get addrepo 127.0.0.1
NOTE: BECAREFUL BEFORE ADDING PLAYER MADE REPOSITORIES.
CHMOD
+
add
-
remove
-R
recruisive
u
user
g
group
o
other or guest
r
read
w
write
x
execute

always run with sudo

Examples

This will remove read permission from owner of file.
sudo chmod u-r /home/<USER>/document.txt

This will grant write and execute permissions to others (and guest) and apply these permissions inside every single thing in subfolders in target folder.
sudo chmod -R o+wx /home/<USER>/myfolder
HTTP

    You need libhttp exploits to hack a http service.
    You need shell access to connect.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.

Sample exploit to connect HTTP
SSH

    You need libssh exploits to hack a ssh service.
    If you know user name and password, use ssh command to connect. You still can use exploits to connect which provide shell access.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.or you can use ssh command if you know user name and password.

Sample exploit to connect SSH
FTP

    You need libftp exploits to hack a ftp service.
    If you know user name and password, use ftp command to connect. You still can use exploits to connect which provide shell access.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.or you can use ftp command if you know user name and password.

Sample exploit to connect FTP
SMTP

    You need libsmtp exploits to hack a smtp service.
    Use smtp-user-list to get registered user list.
    You need shell access to connect.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.

Sample exploit to connect SMTP
CHAT ROOMS

    You need libchat exploits to hack chat rooms.
    You need shell access to connect machine that runs chat room.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.

Sample exploit to connect CHAT SERVICES
CLOSED PORTS

    You need libsql exploits to hack a closed port.
    You need to be in that network to be able to connect ot hack closed port.
    You need shell access to connect.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.

Sample exploit to connect
WEB CAMS / SECURITY CAMS

    You need libcam exploits to hack a security cam.
    You need shell access to connect.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.
    Security code is root password.

Sample exploit to connect
RSHELL SERVER

    You need librshell exploits to hack a rshell service.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.

Sample exploit to connect RSHELL SERVER
STUDENTS / EMPLOYEES / CRIMINAL

    You need libsql exploits to hack.
    If it is closed port, you need to be in that network to be able to connect.
    You need shell access to connect.
    Read exploit description on hack shop. If it says Get access to a shell, you can use it to connect.

Sample exploit to connect
NO PORTS

    If you do not see any ports, connect router. Click "How to Connect" on right for detailed info.
    You can try rshell and you can use funny game template in mail program to send rshell to NPC. Click "Social Engineering" on right and read "Funny Game".
    Click "How to Hack Other Devices in Network" on right to see more info.

ROUTERS

    You need kernel_router exploits to hack a router.
    You need exploits to connect routers.
    Use scanrouter tool to learn version number.

Sample exploit to connect ROUTER
FIREWALLS

    You need kernel_router exploits to disable firewalls.

Sample exploit to disable firewalls
Local Libraries

    Default local libraries are kernel_module.so, net.so and init.so.
    On the hack shop, kernel_module exploit is for kernel_module.so
    On the hack shop, net exploit is for net.so
    On the hack shop, init exploit is for init.so
    If you do not know what to type for version number, click How to Find Version Number of a Library on right and read it..

Local Exploits
Go to hack shop and click Exploits tab:



    Select one of local library from drop-down list, type version number and hit search button.
    Click one of them and read description. You'll see what can it do and what does it need to work.
    If you do not know what to choose, click Local Libraries on right.
    Click "How to Hack (Local Hack)" on right and read.

How to Find Version Number of a Library

    Try Social Engineering (choose System information as subject)
    If you don't know where to find names and e-mails for social engineering, Click "How to Find Names and E-Mails for Social Engineering" on right and read it.
    If you want to know version number of a library that currently located in your computer, make a custom script or copy the code below and paste into Code Editor. Click build button, save it in your home folder and run it.

metaxploit = include_lib("/lib/metaxploit.so")
if not metaxploit then metaxploit = include_lib(current_path + "/metaxploit.so")
if not metaxploit then exit("Can't find metaxploit.so in /lib folder or current folder.")
if params.len == 0 then exit("You have to specify full path.\nTarget file must be library.\nusage: "+program_path.split("/")[-1]+" /folder/filename.so")
lib = metaxploit.load(params[0])
if not lib then exit("You have to specify full path.\nTarget file must be library.\nusage: "+program_path.split("/")[-1]+" /folder/filename.so")
print(lib.lib_name+" = "+lib.version)
How to Hack (Remote Hack)
Use nmap and look at the output:

You can see service name and version numbers.

Go to hack shop and click Exploits tab:

    Select library name from drop-down list, type version number and hit search button.
    Click one of them and read description. You'll see what can it do and what does it need to work.
    Download one of them and run.


If you still do not know what to choose, click on a service name on right and read it.

If requirements are not met, exploit will not work.

If it is not working...

    Get another exploit.
    Try to hack other ports.
    If you got a warning about active user/root, try social engineering.
    If you got a warning about mismatched version numbers, try another exploit.
    If there are no ports left to hack, try a router exploit.
    Still no luck? get another job/mission/quest or hack another thing.


If you managed to get in, click "How to Hack (Local Hack) " on right and read it.

If you managed to reset or cracked password of a non-root user or root:

    Make sure yo uare connected. If you are not connected, click "How To Connect" on right and read it.
    If you are guest, use sudo -u command to switch user or sudo -s command to become root.
    If you are non-root, use sudo -s command to become root.


You have to clear logs if you hacked something. If you do not know how to clear logs, click "How to Clear Logs" on right and read it.
How to Hack (Local Hack)
Follow these steps if you managed to connect but you are not root...

    Get a local exploit.
    If you do not know what to choose, click Local Exploits on right and read it.
    Make sure you are connected. If you do not know how to connect, click "How to Connect" on right and read it.
    Before getting a local exploit, use social engineering to learn kernel_module.so, init.so, net.so version numbers.
    To find version number of a library, try Social Engineering
    (choose System information as subject)
    If you don't know where to find names and e-mails for social engineering, Click "How to Find Names and E-Mails for Social Engineering" on right and read it.
    Get kernel_module, init or net exploits from hack shop.(Look for root first. If there is no exploit for root, look for non-root...)
    Run Browser.exe on machine that you are currently connected to download local exploits or upload them from your machine to target /guest folder and run.
    If you got an error about metaxploit.so upload it to target.
    If you got an error about crypto.so, upload it to target.
    If you reset a registered user password, run sudo -u command and switch user.
    If you just got access to a registered user, download passwd file in /etc folder. Crack root password with decipher and run sudo -s command to become root.
    You can use scp -d command to download or run FileExplorer.exe on target then drag&drop file to your computer.
    If it is a FTP server, use get /etc/passwd command to download passwd file.
    If you got root password, run sudo -s command.

If you reset password with pre-generated exploits, look at terminal.You'll eventually see user name.
How to Hack Other Devices in Network

    Do not disconnect or run exit command.
    Use ScanLan.exe command to see existing devices in the network.
    Scan your target lan ip with nmap.
    Upload correct exploits to the device that you already connected and run exploits.

If you still do not know what to choose, click service name on right and read it.

You have to clear logs if you hacked something. If you do not know how to clear logs, click "How to Clear Logs" on right and read it.
How to Connect

    If service is ssh, run ssh command.
    If service is ftp, run ftp command.
    If you want to connect directly to a router, you need exploits that provide shell access.
    If you do not know password, you have to hack target. Click "How to Hack (Remote Hack)" on right and read it.
    You need exploits that provide shell access to connect anything.
    If exploit description says Get access to a shell, you can use it to connect. Otherwise, you can't connect...
    If you already connected and do not know what to do, click "How to Hack (Local Hack)" on the right and read it.

Sample exploit to connect
Exploit is NOT Working

    Get another exploit.
    Try to hack other ports.
    If you got a warning about active user/root, try social engineering.
    If you got a warning about mismatched version numbers, try another exploit.
    If there are no ports left to hack, try a router exploit or rshell.
    Still no luck? get another job/mission/quest or hack another thing.

If requirements are not met, exploit will not work.

If you still do not know what to choose, click a service name on right and read it.
How to Complete Missions
Corrupt Data

    Connect to target. If you don't know how to connect, click "How to Connect" on right and read it.
    You have to be root. If you don't know how to be root, click "How to Get Root" on right and read it.
    Remove /boot folder. You can find and remove it from FileExplorer.exe or just type rm -r /boot and hit enter.
    Run reboot command.
    Answer mail. Type anything and send.

Credentials

    You do not have to be connected to target. You can find an exploit that can crack passwd file without extra work.
    If you couldn't find one or can't crack passwd file, connect to target. If you don't know how to connect, click "How to Connect" on right and read it.
    You need to get at least non-root user privileges.
    Run FileExplorer.exe on target. Open /etc folder and drag&drop passwd file ot your computer and run decipher.
    You can run scp -d /etc/passwd command to download passwd file. After that run decipher.
    If it is a FTP server, use get /etc/passwd command to download passwd file.
    If you obtained credentials, answer mail with just password or user_name:password.

Academic Changes

    Connect to target. If you don't know how to connect, click "How to Connect" on right and read it.
    You have to get at least non-root user privileges. If you don't know how to do it, click "How to Hack (Remote Hack)" on right and read it.
    Run FileExplorer.exe on target and search for StudentsViewer.exe
    Run StudentsViewer.exe on target, find person and change records. It must be at least 7.0
    Answer mail. Type anything and send.

Police Records

    Connect to target. If you don't know how to connect, click "How to Connect" on right and read it.
    You have to get at least get non-root user privileges. If you don't know how to do it, click "How to Hack (Remote Hack)" on right and read it.
    Run FileExplorer.exe on target and search for PoliceRecord.exe
    Run PoliceRecord.exe on target, find person and change records.
    Answer mail. Type anything and send.

You have to clear logs if you hacked something. If you do not know how to clear logs, click "How to Clear Logs" on right and read it.
HIDDEN MISSIONS - Where to Find Hidden Missions
What is hidden mission?
Hidden mission is a mission type that can not be found on hackshops. There are a lot hidden missions and those hidden missions are more diverse than hackshop missions.
How to find hidden missions?

    Hack something and look around, read logs, text and pdf files.
    Try to figure out what is happening or what was happened.
    Try to solve puzzle

POLICE MISSIONS - Where to Find Police Missions
You can work for police and in return for this you gain positive karma.

    Open Browser and type police and hit enter.
    Visit one of websites to get/find jobs and missions.

CRYPTO CURRENCIES
Crypto currencies are an alternative to game's banking system. Just like real world. You can mine or buy and sell them.

Note: Crypto Currencies are player driven.

    If you want to create your own crypto currency, click "How to Create Yout Own Crypto Currency on right and read it".
    If you want to but or sell, click "How to Buy and Sell Crypto Currency on right and read it".
    If you want to mine, click "How to Mine Crypto Currency on right and read it".
    If you want to watch current prices click "Crypto Currency Prices and Conversation Rates" and read it.

INTERNET SERVICE PROVIDERS
You can subscribe for better connection also when you subscribe, you get cable connection that can not be controlled by NPCs.

    Open Browser and type internet and hit enter.
    Visit one of those websites.
    Click Service tab
    Choose a desired connection speed
    Click "Home Internet" to subscribe.
    When you subscribed, you get cable conection.
    Click wi-fi icon to switch between cable and wi-fi.

Social Engineering
Administrative action

    Use this to get user password.
    You can use whois command to get extra info.
    You can use smtp-user-list to get extra info.

Login issues

    Use this to get user password.
    You can use whois command to get extra info.
    You can use smtp-user-list to get extra info.

Online user

    Use this to make non-root user online.
    Company name is domain name.
    You can use whois command to get extra info.
    You can use smtp-user-list to get extra info.

Admin online

    Use this to make root online.
    Company name is domain name.
    You can use whois command to get extra info.

System information

    Use this to learn installed library version numbers on target.
    You have to send a mail to a non-root user.
    You have to type one of non-root user name into [Name] field.
    You have to write library name. (example: net.so)

Remote Login

    self-explanatory

Funny Game

    It is exclusive for rshell.
    Set up your rshell server
    If you do not know how to set up a server,
    click "How to Set Up a Server" on right and read it.
    Then click attachment.
    Fill empty fields and click apply and then send it.
    Now run rshell-interface.

CCTV Access Request

    It is for web/security cams.


If you don't know where to find names and e-mails for social engineering, Click "How to Find Names and E-Mails for Social Engineering" on right and read it.
How to Find Names and E-Mails for Social Engineering

    You can get names from EmployeesViewer.exe, if it is exists in server.
    If you already connected, check /home older to find names.
    If you can't navigate, you can use ls /home command.
    Look at Config folder in user folder to find e-mail addresses.
    If you can't navigate, you can use cat command.(example: cat /home/<USER>/Config/Mail.txt)
    You can use smtp-user-list to find e-mail addresses but it only works if SMTP service is running.

How to Get Root

    If you are connected and know password, use sudo -s command to get root.
    If you don't know password and not connected, click "How to Hack (Remote Hack)" on right and read it.
    If you already connected but do not know password, click "How to Hack (Local Hack)" on right and read it.
    If you do not know how to connect, click "How to Connect" on right and read it.

How to Switch User or Login as Another User

    If you are not connected to target but you know password and it is ssh, run ssh command.
    If you are not connected to target but you know password and it is ftp, run ftp command.
    If you are in and you know password, use sudo -u command.
    If you don't know password, click "How to Hack (Remote Hack)" on right and read it.
    If you are not connected to target or don't know how to connect or you can't connect, click "How to Connect" on right and read it.

How to Hack Bank Accounts

    You do not have to be connected to target. You can find an exploit that can crack bank passwords without extra work.
    If you couldn't find one or can't crack, connect to target. If you don't know how to connect, click "How to Connect" on right and read it.
    You can run FileExplorer.exe on target and navigate in /home/<USER>
    Enter Config folder and drag&drop Bank.txt file on your computer and run decipher.
    You can use scp -d /home/<USER>/Config/Bank.txt command to download credentials.
    If it is a FTP server, use get /home/<USER>/Config/Bank.txt command to download Bank.txt file.
    After that, run decipher
    If you don't know any user name to use command above, use ls /home command to see user names / folders.


You can't hack bank systems right now. You can only hack bank accounts separately.
How to Hack E-Mail Accounts

    Connect to target. If you don't know how to connect, click "How to Connect" on right and read it.
    You can run FileExplorer.exe on target and navigate in /home/<USER>
    Enter Config folder and drag&drop Mail.txt file on your computer and run decipher.
    You can use scp -d /home/<USER>/Config/Mail.txt command to download credentials.
    If it is a FTP server, use get /home/<USER>/Config/Mail.txt command to download Mail.txt file.
    After that, run decipher
    If you don't know any user name to use command above, use ls /home command to see user names / folders.


You can hack e-mail services / websites.

    Hack and get in any mail server.
    You can find acounts and password in database.
    If you got the database, you can open and view it or run decipher and crack passwords.
    If you don't know how to hack, click "How to Hack" on right and read it.

How to Hack Routers

    Run scanrouter to learn version number of target router.
    Download a kernel_router exploit from hack shop.
    Run exploit.
    You can hack servers/computers with router exploits.
    You can hack passwords/files/folders with router exploits.


If exploit is not working, get another exploit.
How to Clear Logs

    You have to be root to be able to clear logs. If you don't know how to be root, click "How to Get Root" on right and read it.
    Run LogViewer.exe on target, clear shell obtained, connection routed and file deleted entries and hit save button.
    If it is FTP, use cd /var command to enter var folder and then use get system.log command to download log file. Open and edit downloaded log file and re-upload with put system.log command.

How to Crack / Scan Faster

    Buy better CPU from regular shop.
    Faster CPU will crack passwords and scan other things faster.

Download & Upload in FTP

    Use put command to upload something.
    Use get command to download something.
    You can only run pre-defined commands in FTP servers.
    Run help command to get more info.

FIREWALL CONFIGURATION

    Use ifconfig command and learn what is gateway ip.
    Open Browser.
    Type your gateway ip into addressbar and add :8080
    It should look like this:

    Click Add Entry button and fill cmpty fileds like this:

    Click Firewall.
    Click Add Entry button.
    Fill the empty fields and make sure you do not block your own computer.
    Then click Save Button to save your configuration.

How to Protect Yourself

    To protect yourself, you need to remove guest permissions on all files. Run this command:
    sudo chmod -R o-rwx /
    Get 1 or more rented servers and use them all the time.
    To Protect your rented servers, enter following commands:
    (if you are going to create and use non-root users, this may not suitable for you)
    (Make sure there are no way to get root access for current version of your rented server)
    sudo chmod -R o-rwx /
    sudo chmod -R g-rwx /
    sudo chmod -R u-rwx /
    Click "How to Survive" on right and read it.
    Click "How to Survive from Sniffers " on right and read it.
    Click "How to Survive from Rshells" on right and read it.

How to Survive

    Read whole "Getting Started" and "Websited & Services" sections in Game Manual.
    Rent servers and use them for everything. They are hiding your real IP also best way to deal with NPCs.
    IF you do not know how to rent servers, click "How to Rent Servers" on right and read it.
    DO NOT forget to clear suspicious logs. If you do not know how to clear logs, click "How to Clear Logs" on right and read it.
    Click "How to Protect Yourself" on right and read it.
    If you are on online mode, do not run anything you do not trust.

SAFE MODE
What is safe mode?
Safe mode is where you will be redirected when important system files deleted, moved or damaged.
In safe mode there is only command line and you can't run File Explorer and other similar programs but you can always run command-line based programs
Safe mode gives you a chance to restore your system.
If you want to enter safe mode manually, run this command:
reboot -sm
How to Survive from Rshells
If you are on single player, no need to worry.

If you are on online mode:

    Best thing you can do is not installing things you do not trust.
    2nd best thing you can do is running ps command. Once you run it, you will see current working processes on the machine. If you see something unusual there, use kill command with sudo to terminate rshell connection.

How to Survive from Sniffers
If you are on single player, no need to worry.

If you are on online mode:

    Open /server/conf/sshd.conf with notepad and
    replace
    "encryption_enabled": false,
    with
    "encryption_enabled": true,
    then save it.
    ssh-server is shipped with a basic encryption algorithm but it is not hard to crack by a skilled programmer. Make your own encryption or find a trusted one if you want a better encryption.

How to Update
Run those two commands (click apt-get on right and read it for more details)
sudo apt-get update
sudo apt-get upgrade
Additionally, you can follow these steps as an alternate solution

    For ssh (libssh), ftp (libftp), http (libhttp), chat (libchat), rshell (librshell), repository (librepository) download them again from regular shop when they updated and run / install.
    For init.so, kernel_module.so, net.so, download them from npc servers when they updated.
    For crypto.so and metaxploit.so, download them again from hack shop when they updated.
    kernel_router.so of your network, gets auto update.
    kernel_router.so of your rented server can't be updated.

How to Get Faster Connction Speed

    Open Browser and type internet and hit enter.
    Visit one of those websites.
    Click Service tab
    Choose a desired connection speed
    Click "Home Internet" to subscribe.
    When you subscribed, you get cable conection.
    Click wi-fi icon to switch between cable and wi-fi.

How to Rent a Server

    Open Browser and type internet and hit enter.
    Visit one of those websites.
    Click Service tab
    Choose a desired connection speed
    Click Rent Server.
    Click "Download ConfigLan" to download ConfigLAn so you can manage your servers.

How to Upgrade Rented Servers

    Log in your rented server.
    Open Browser.exe on your rented server, go to a regular shop and buy hardware.
    Open Settings.exe on your rented server and replace hardware.

How to Set Up a Server

    Visit regular shop or hack shop to find required files.
    If you don't know where to find required files, click "Required Files & Ports to Set Up a Server" on right and read it.
    Find the required file and then download it.
    If you don't know where to rent a server, click "How to Rent a Server" on right and read it.
    Put it on your rented server and run it on your rented server with sudo or root permissions
    (you can use your own computer but it is risky)
    Use ifconfig command and learn what are gateway ip and local ip.
    Open Browser on your rented server.
    Type your gateway ip into addressbar and add :8080
    It should look like this:

    Click Add Entry button and fill cmpty fileds like this:

    If you don't know what port number you have to type, click "Required Files & Ports to Set Up a Server" on right and read it.
    Then click Save Button to save your configuration.

Required Files & Ports to Set Up a Server
SERVICE NAME
REQUIRED FILE
WHERE TO FIND
PORT NUMBER
FTP
ftp-server
regular shop
21
SSH
ssh-server
regular shop
22
HTTP
http-server
regular shop
80
RSHELL
rshell-server
hack shop
1222
REPOSITORY
repository-server
regular shop
1542
CHAT
chat-server
regular shop
6667
How to Create Your Own Crypto Currency

    Open Browser and type coin and hit enter.
    Visit one of those websites.
    Click Service tab
    Fill the empty fields
    Click "Create Coin".

How to Buy and Sell Crypto Currency

    Visit a regular shop and download Stocks.exe and wallet program.
    If you do not know where to find shops Click "Where to Find Shops" on right and read it.
    Run wallet and create a wallet account and then run Stocks.exe to buy and sell crypto currency.

How to Mine Crypto Currencies

    Crypto currencies are player driven. This means you have to find miner program of the crypto currency that you want to mine.
    Run only if you trust the owner(s).
    If you want to mine crypto currencies faster, click "How to Mine Crypto Currencies Faster" on right and read it.

How to Mine Crypto Currencies Faster
Buy better GPU.
Crypto Currency Prices and Conversation Rates

    Crypto Currencies are player driven. This means there is no fixed conversation rate.
    To view current state of a crypto currency, download Stocks.exe from regular shop.
    If you do not know where to find shops click "Where to find shops" on right and read it.
    Run Stocks.exe, choose desired crypto currency at top left. Now you can view detailed graphics and prices for the selected crypto currency.
    If you want to buy and sell crypto currency, click "How to Buy and Sell Crypto Currency" on right and read it.

How to Install Themes

    Open Notepad
    Copy and paste theme content into Notepad and save it somewhere.
    Open Settings -> Appearance and click three dots and choose saved theme file.
    Click Apply changes.

How to Install Custom Wallpaper

    Open Settings -> Appearance and move your mouse over yellow icon.
    It will tell you where to copy your custom wallpaper and other details.
    After copying your wallpaper to right folder, close appearance and re-open it.
    Click dropdown menu and choose your wallpaper.
    Click Apply changes.

Gifts.txt

    Gifts.txt is a text file that contains details of a random wifi network.
    You'll get it after fresh start but only if you have completed tutorial mission once.
    If you do not want to see it everytime you start a fresh game, Open Preferences, click Tutorial and untick Enable Gifts.txt box.

SOURCE CODE

    Open Manual and click Terminal Programs.
    Choose a category.
    Select a tool.
    Click Source Code button.


Alternative Way

To view source code of official tools, use following command

CodeEditor.exe -code tool_name

Sample Usage

CodeEditor.exe -code ping

CodeEditor.exe -code wallet

CodeEditor.exe -code nmap
