// ╔═══════════════════════════════════════════════════════════════════════════════╗
// ║                            WAIFUP3N v2.1.3                                   ║
// ║                   Advanced WiFi Penetration Framework                         ║
// ║                        for Grey Hack Game                                     ║
// ╚═══════════════════════════════════════════════════════════════════════════════╝

// Global Configuration
globals.WAIFUP3N_VERSION = "2.1.3"
globals.AUTHOR = "Elite Hacker"
globals.BUILD_DATE = "2025"

// Core Libraries Import with Error Handling
crypto = include_lib("/lib/crypto.so")
if not crypto then crypto = include_lib(current_path + "/crypto.so")
if not crypto then
    print("<color=red>[FATAL] crypto.so library not found!</color>")
    print("<color=yellow>Please ensure crypto.so is in /lib/ or current directory</color>")
    exit("Missing required library")
end if

metaxploit = include_lib("/lib/metaxploit.so")
if not metaxploit then metaxploit = include_lib(current_path + "/metaxploit.so")

// Initialize Core Objects
shell = get_shell
computer = shell.host_computer
current_path = shell.host_computer.current_path

// Advanced Configuration Parameters
CONFIG = {}
CONFIG.MIN_ACKS = 7500
CONFIG.MAX_ACKS = 15000
CONFIG.MIN_POWER = 15
CONFIG.CRACK_TIMEOUT = 300
CONFIG.AUTO_CONNECT = false
CONFIG.SAVE_RESULTS = true
CONFIG.VERBOSE = true
CONFIG.SCAN_INTERVAL = 2
CONFIG.MAX_RETRIES = 3
CONFIG.WORDLIST_MODE = false

// Animation and Display System
DISPLAY = {}

// ASCII Art Collection
DISPLAY.banner = [
    "                                                                    ",
    "██╗    ██╗ █████╗ ██╗███████╗██╗   ██╗██████╗ ██████╗ ███╗   ██╗",
    "██║    ██║██╔══██╗██║██╔════╝██║   ██║██╔══██╗╚════██╗████╗  ██║",
    "██║ █╗ ██║███████║██║█████╗  ██║   ██║██████╔╝ █████╔╝██╔██╗ ██║",
    "██║███╗██║██╔══██║██║██╔══╝  ██║   ██║██╔═══╝  ╚═══██╗██║╚██╗██║",
    "╚███╔███╔╝██║  ██║██║██║     ╚██████╔╝██║     ██████╔╝██║ ╚████║",
    " ╚══╝╚══╝ ╚═╝  ╚═╝╚═╝╚═╝      ╚═════╝ ╚═╝     ╚═════╝ ╚═╝  ╚═══╝",
    "                                                                    "
]

DISPLAY.hacker_art = [
    "                    _.---._     .---.",
    "        __...---' .---. `---'-.   `.",
    "   .-''__.--' _.'( | )`.  `.  `._ :",
    ".'__-'_ .--'' ._`---'_.-.  `.   `-`.",
    "       ~ -._ -._``---. -.    `-._   `.",
    "            ~ -.._ _ _ _ ..-_ `.  `-._``--.._",
    "                         -~ -._  `-.  -. `-._``--.._.--''.",
    "                                ~ ~-.__     -._  `-.__   `. `.",
    "                                        ~~ ~---...__ _    ._ .`"
]

DISPLAY.loading_chars = ["|", "/", "-", "\\"]
DISPLAY.colors = {
    "primary": "cyan",
    "secondary": "yellow", 
    "success": "green",
    "error": "red",
    "warning": "yellow",
    "info": "blue",
    "accent": "magenta"
}

// Advanced Display Functions
print_colored = function(color, text, newline)
    if newline == null then newline = true
    output = "<color=" + color + ">" + text + "</color>"
    if newline then
        print(output)
    else
        print(output, false)
    end if
end function

print_gradient = function(text, color1, color2)
    // Simulate gradient effect
    mid = floor(len(text) / 2)
    part1 = text[0:mid]
    part2 = text[mid:]
    print_colored(color1, part1, false)
    print_colored(color2, part2, true)
end function

animate_loading = function(message, duration)
    if not CONFIG.VERBOSE then return
    
    start_time = time
    counter = 0
    
    while time - start_time < duration
        char_idx = counter % len(DISPLAY.loading_chars)
        loading_char = DISPLAY.loading_chars[char_idx]
        
        print_colored(DISPLAY.colors.info, "[" + loading_char + "] " + message, false)
        wait(0.2)
        print(char(13), false) // Carriage return
        counter = counter + 1
    end while
    
    print_colored(DISPLAY.colors.success, "[✓] " + message + " - Complete!")
end function

display_banner = function()
    print_colored(DISPLAY.colors.primary, "═" * 80)
    for line in DISPLAY.banner
        print_gradient(line, DISPLAY.colors.primary, DISPLAY.colors.accent)
    end for
    print("")
    print_colored(DISPLAY.colors.secondary, "    Advanced WiFi Penetration Framework v" + globals.WAIFUP3N_VERSION)
    print_colored(DISPLAY.colors.info, "           Designed for Grey Hack Cybersecurity Training")
    print_colored(DISPLAY.colors.primary, "═" * 80)
    print("")
end function

display_hacker_art = function()
    for line in DISPLAY.hacker_art
        print_colored(DISPLAY.colors.accent, line)
        wait(0.1)
    end for
    print("")
end function

// Advanced Logging System
LOGGER = {}
LOGGER.log_file = "waifup3n_log.txt"
LOGGER.results_file = "waifup3n_results.txt"

log_message = function(level, message)
    timestamp = time
    log_entry = "[" + timestamp + "] [" + level + "] " + message
    
    // Display to console
    color = DISPLAY.colors.info
    if level == "ERROR" then color = DISPLAY.colors.error
    if level == "SUCCESS" then color = DISPLAY.colors.success
    if level == "WARNING" then color = DISPLAY.colors.warning
    
    print_colored(color, "[" + level + "] " + message)
    
    // Save to log file if enabled
    if CONFIG.SAVE_RESULTS then
        log_file = computer.File(LOGGER.log_file)
        if log_file.has_permission("r") then
            content = log_file.get_content + log_entry + "\n"
        else
            content = "WAIFUP3N Log File - Started: " + timestamp + "\n" + log_entry + "\n"
        end if
        log_file.set_content(content)
    end if
end function

log_info = function(message)
    log_message("INFO", message)
end function

log_success = function(message)
    log_message("SUCCESS", message)
end function

log_warning = function(message)
    log_message("WARNING", message)
end function

log_error = function(message)
    log_message("ERROR", message)
end function

// Network Interface Management
NETWORK = {}

get_wifi_interfaces = function()
    log_info("Scanning for wireless network interfaces...")
    animate_loading("Detecting network hardware", 2)
    
    net_devices = computer.network_devices
    if not net_devices or len(net_devices) == 0 then
        log_error("No network devices found!")
        return []
    end if
    
    devices = net_devices.split(" ")
    wifi_interfaces = []
    
    for device in devices
        // Check for common WiFi interface naming patterns
        if device.indexOf("wlan") != null or device.indexOf("wifi") != null or 
           device.indexOf("wl") != null or device.indexOf("ath") != null then
            wifi_interfaces.push(device)
        end if
    end for
    
    // If no WiFi-specific interfaces found, use first available
    if len(wifi_interfaces) == 0 and len(devices) > 0 then
        wifi_interfaces.push(devices[0])
        log_warning("No WiFi-specific interfaces found, using: " + devices[0])
    end if
    
    log_success("Found " + len(wifi_interfaces) + " wireless interface(s)")
    return wifi_interfaces
end function

select_interface = function(interfaces)
    if len(interfaces) == 1 then
        return interfaces[0]
    end if
    
    print_colored(DISPLAY.colors.info, "\nAvailable WiFi Interfaces:")
    print_colored(DISPLAY.colors.primary, "─" * 40)
    
    for i in range(0, len(interfaces) - 1)
        print_colored(DISPLAY.colors.secondary, (i + 1) + ". " + interfaces[i])
    end for
    
    print("")
    choice = 0
    while choice < 1 or choice > len(interfaces)
        choice_str = user_input("Select interface (1-" + len(interfaces) + "): ")
        if choice_str != "" then choice = choice_str.val
    end while
    
    return interfaces[choice - 1]
end function

// Advanced Network Scanning
scan_networks = function(interface)
    log_info("Initiating comprehensive WiFi network scan...")
    animate_loading("Scanning wireless spectrum", 3)
    
    networks = computer.wifi_networks(interface)
    if not networks or len(networks) == 0 then
        log_error("No WiFi networks detected!")
        return []
    end if
    
    log_success("Discovered " + len(networks) + " wireless networks")
    return networks
end function

parse_network_data = function(networks)
    log_info("Analyzing network data and signal strength...")
    parsed_networks = []
    
    for network in networks
        parts = network.split(" ")
        if len(parts) >= 3 then
            bssid = parts[0]
            power_str = parts[1]
            essid = parts[2]
            
            // Extract power level
            power = 0
            if power_str.indexOf("%") != null then
                power_clean = power_str.replace("%", "")
                power = power_clean.val
            end if
            
            // Security analysis
            security = "Unknown"
            if essid.indexOf("_WEP") != null then security = "WEP"
            if essid.indexOf("_WPA") != null then security = "WPA/WPA2"
            if essid.indexOf("_Open") != null then security = "Open"
            
            // Channel estimation (simplified)
            channel = floor(rnd * 11) + 1
            
            network_data = {
                "bssid": bssid,
                "essid": essid,
                "power": power,
                "security": security,
                "channel": channel,
                "raw": network,
                "vulnerability_score": calculate_vulnerability_score(power, security, essid)
            }
            
            parsed_networks.push(network_data)
        end if
    end for
    
    return sort_networks_by_vulnerability(parsed_networks)
end function

calculate_vulnerability_score = function(power, security, essid)
    score = power // Base score from signal strength
    
    // Security penalties/bonuses
    if security == "Open" then score = score + 50
    if security == "WEP" then score = score + 30
    if security == "WPA/WPA2" then score = score + 10
    
    // Common vulnerable ESSID patterns
    if essid.indexOf("default") != null then score = score + 20
    if essid.indexOf("admin") != null then score = score + 15
    if essid.indexOf("guest") != null then score = score + 25
    if essid.indexOf("test") != null then score = score + 20
    
    return score
end function

sort_networks_by_vulnerability = function(networks)
    // Bubble sort by vulnerability score (highest first)
    for i in range(0, len(networks) - 1)
        for j in range(i + 1, len(networks) - 1)
            if networks[i].vulnerability_score < networks[j].vulnerability_score then
                temp = networks[i]
                networks[i] = networks[j]
                networks[j] = temp
            end if
        end for
    end for
    return networks
end function

display_network_table = function(networks)
    print("")
    print_colored(DISPLAY.colors.primary, "╔═══════════════════════════════════════════════════════════════════════════╗")
    print_colored(DISPLAY.colors.primary, "║                        DISCOVERED WIRELESS NETWORKS                      ║")
    print_colored(DISPLAY.colors.primary, "╠═══════════════════════════════════════════════════════════════════════════╣")
    
    header = "║ # │ BSSID             │ Power │ Channel │ Security  │ ESSID            │ Score ║"
    print_colored(DISPLAY.colors.secondary, header)
    print_colored(DISPLAY.colors.primary, "╠═══════════════════════════════════════════════════════════════════════════╣")
    
    for i in range(0, len(networks) - 1)
        net = networks[i]
        num = str(i + 1)
        if len(num) == 1 then num = " " + num
        
        bssid_display = net.bssid
        if len(bssid_display) > 17 then bssid_display = bssid_display[0:14] + "..."
        while len(bssid_display) < 17 do
            bssid_display = bssid_display + " "
        end while
        
        power_display = str(net.power) + "%"
        while len(power_display) < 5 do
            power_display = " " + power_display
        end while
        
        channel_display = str(net.channel)
        while len(channel_display) < 7 do
            channel_display = " " + channel_display
        end while
        
        security_display = net.security
        while len(security_display) < 9 do
            security_display = security_display + " "
        end while
        
        essid_display = net.essid
        if len(essid_display) > 16 then essid_display = essid_display[0:13] + "..."
        while len(essid_display) < 16 do
            essid_display = essid_display + " "
        end while
        
        score_display = str(net.vulnerability_score)
        while len(score_display) < 5 do
            score_display = " " + score_display
        end while
        
        // Color code by vulnerability
        color = DISPLAY.colors.info
        if net.vulnerability_score > 80 then color = DISPLAY.colors.error
        if net.vulnerability_score > 60 then color = DISPLAY.colors.warning
        
        row = "║" + num + " │ " + bssid_display + " │" + power_display + " │" + channel_display + " │ " + security_display + " │ " + essid_display + " │" + score_display + " ║"
        print_colored(color, row)
    end for
    
    print_colored(DISPLAY.colors.primary, "╚═══════════════════════════════════════════════════════════════════════════╝")
    print("")
end function

// Attack Management System
ATTACK = {}

select_target = function(networks)
    if len(networks) == 0 then
        log_error("No networks available for selection")
        return null
    end if
    
    display_network_table(networks)
    
    print_colored(DISPLAY.colors.info, "Target Selection Options:")
    print_colored(DISPLAY.colors.secondary, "1-" + len(networks) + ": Select specific network")
    print_colored(DISPLAY.colors.secondary, "A: Auto-select highest vulnerability score")
    print_colored(DISPLAY.colors.secondary, "Q: Quit")
    print("")
    
    while true
        choice = user_input("Enter your choice: ").upper()
        
        if choice == "Q" then
            log_info("Attack cancelled by user")
            exit()
        end if
        
        if choice == "A" then
            log_info("Auto-selecting target with highest vulnerability score")
            return networks[0]
        end if
        
        if choice != "" then
            choice_num = choice.val
            if choice_num >= 1 and choice_num <= len(networks) then
                selected = networks[choice_num - 1]
                log_success("Target selected: " + selected.essid + " (Score: " + selected.vulnerability_score + ")")
                return selected
            end if
        end if
        
        log_warning("Invalid selection. Please try again.")
    end while
end function

monitor_mode_control = function(interface, action)
    if action == "start" then
        log_info("Activating monitor mode on " + interface)
        animate_loading("Configuring wireless interface for monitoring", 2)
        result = crypto.airmon("start", interface)
        log_success("Monitor mode activated")
    else
        log_info("Deactivating monitor mode on " + interface)
        animate_loading("Restoring normal interface operation", 1)
        result = crypto.airmon("stop", interface)
        log_success("Monitor mode deactivated")
    end if
    return result
end function

execute_handshake_capture = function(target, interface)
    log_info("Initiating WPA handshake capture for: " + target.essid)
    print_colored(DISPLAY.colors.warning, "⚠️  WARNING: Do not interrupt the capture process!")
    print_colored(DISPLAY.colors.info, "Target Details:")
    print_colored(DISPLAY.colors.secondary, "  • BSSID: " + target.bssid)
    print_colored(DISPLAY.colors.secondary, "  • ESSID: " + target.essid)
    print_colored(DISPLAY.colors.secondary, "  • Power: " + target.power + "%")
    print_colored(DISPLAY.colors.secondary, "  • Security: " + target.security)
    print("")
    
    // Dynamic ACK calculation based on signal strength
    target_acks = CONFIG.MIN_ACKS
    if target.power < 30 then
        target_acks = CONFIG.MAX_ACKS
        log_warning("Low signal strength detected, increasing ACK target to " + target_acks)
    end if
    
    start_time = time
    log_info("Collecting " + target_acks + " acknowledgment packets...")
    
    // Execute aireplay attack
    result = crypto.aireplay(target.bssid, target.essid, target_acks)
    
    end_time = time
    duration = end_time - start_time
    log_success("Handshake capture completed in " + duration + " seconds")
    
    return result
end function

execute_password_crack = function(target)
    log_info("Initiating password cracking for: " + target.essid)
    animate_loading("Loading captured handshake data", 1)
    
    cap_file = computer.File("file.cap")
    if not cap_file.has_permission("r") then
        log_error("Cannot access capture file!")
        return null
    end if
    
    log_info("Analyzing captured WPA handshake...")
    animate_loading("Executing dictionary attack", 5)
    
    start_crack_time = time
    password = crypto.aircrack(cap_file.path)
    crack_duration = time - start_crack_time
    
    // Cleanup capture file
    cap_file.delete()
    
    if password then
        log_success("Password successfully cracked in " + crack_duration + " seconds!")
        log_success("Network: " + target.essid + " | Password: " + password)
        return password
    else
        log_error("Password cracking failed - handshake may be incomplete or password not in dictionary")
        return null
    end if
end function

// Results Management
save_attack_results = function(target, password, success)
    if not CONFIG.SAVE_RESULTS then return
    
    timestamp = time
    result_file = computer.File(LOGGER.results_file)
    
    header = "WAIFUP3N Attack Results Log\n" + "=" * 50 + "\n"
    
    if result_file.has_permission("r") then
        content = result_file.get_content
    else
        content = header
    end if
    
    entry = "\n[" + timestamp + "] Attack Results:\n"
    entry = entry + "Target ESSID: " + target.essid + "\n"
    entry = entry + "Target BSSID: " + target.bssid + "\n"
    entry = entry + "Signal Power: " + target.power + "%\n"
    entry = entry + "Security Type: " + target.security + "\n"
    entry = entry + "Vulnerability Score: " + target.vulnerability_score + "\n"
    entry = entry + "Attack Status: " + (success and "SUCCESS" or "FAILED") + "\n"
    if success then
        entry = entry + "Cracked Password: " + password + "\n"
    end if
    entry = entry + "-" * 50 + "\n"
    
    content = content + entry
    result_file.set_content(content)
    
    log_info("Attack results saved to " + LOGGER.results_file)
end function

// Connection Management
attempt_connection = function(interface, target, password)
    if not CONFIG.AUTO_CONNECT then
        connect_choice = user_input("Attempt connection to " + target.essid + "? (y/n): ").lower()
        if connect_choice != "y" and connect_choice != "yes" then
            return false
        end if
    end if
    
    log_info("Attempting connection to " + target.essid + "...")
    animate_loading("Establishing wireless connection", 3)
    
    result = computer.connect_wifi(interface, target.bssid, target.essid, password)
    
    if result then
        log_success("Successfully connected to " + target.essid)
        
        // Verify connection
        wait(2)
        current_ip = computer.local_ip
        if current_ip and current_ip != "127.0.0.1" then
            log_success("IP Address assigned: " + current_ip)
            
            // Test connectivity
            ping_result = shell.launch("/bin/ping", ["*******", "-c", "1"])
            if ping_result then
                log_success("Internet connectivity confirmed")
            end if
        end if
        
        return true
    else
        log_error("Connection failed - verify password accuracy")
        return false
    end if
end function

// Advanced Statistics and Reporting
generate_session_report = function()
    print("")
    print_colored(DISPLAY.colors.primary, "╔═══════════════════════════════════════════════════════════════════════════╗")
    print_colored(DISPLAY.colors.primary, "║                           SESSION REPORT                                 ║")
    print_colored(DISPLAY.colors.primary, "╚═══════════════════════════════════════════════════════════════════════════╝")
    
    // Read log file for statistics
    log_file = computer.File(LOGGER.log_file)
    if log_file.has_permission("r") then
        content = log_file.get_content
        lines = content.split("\n")
        
        success_count = 0
        error_count = 0
        
        for line in lines
            if line.indexOf("[SUCCESS]") != null then success_count = success_count + 1
            if line.indexOf("[ERROR]") != null then error_count = error_count + 1
        end for
        
        print_colored(DISPLAY.colors.info, "Session Statistics:")
        print_colored(DISPLAY.colors.success, "  • Successful Operations: " + success_count)
        print_colored(DISPLAY.colors.error, "  • Errors Encountered: " + error_count)
        print_colored(DISPLAY.colors.secondary, "  • Log File: " + LOGGER.log_file)
        print_colored(DISPLAY.colors.secondary, "  • Results File: " + LOGGER.results_file)
    end if
    
    print("")
end function

// Configuration Management
parse_command_line = function()
    if not locals.hasIndex("params") or len(params) == 0 then return
    
    i = 0
    while i < len(params)
        param = params[i]
        
        if param == "--help" or param == "-h" then
            display_help()
            exit()
        else if param == "--verbose" or param == "-v" then
            CONFIG.VERBOSE = true
        else if param == "--quiet" or param == "-q" then
            CONFIG.VERBOSE = false
        else if param == "--auto-connect" then
            CONFIG.AUTO_CONNECT = true
        else if param == "--min-power" and i + 1 < len(params) then
            CONFIG.MIN_POWER = params[i + 1].val
            i = i + 1
        else if param == "--min-acks" and i + 1 < len(params) then
            CONFIG.MIN_ACKS = params[i + 1].val
            i = i + 1
        else if param == "--max-acks" and i + 1 < len(params) then
            CONFIG.MAX_ACKS = params[i + 1].val
            i = i + 1
        else if param == "--no-save" then
            CONFIG.SAVE_RESULTS = false
        end if
        
        i = i + 1
    end while
end function

display_help = function()
    print_colored(DISPLAY.colors.primary, "WAIFUP3N v" + globals.WAIFUP3N_VERSION + " - Advanced WiFi Penetration Framework")
    print("")
    print_colored(DISPLAY.colors.info, "USAGE:")
    print_colored(DISPLAY.colors.secondary, "  waifup3n [OPTIONS]")
    print("")
    print_colored(DISPLAY.colors.info, "OPTIONS:")
    print_colored(DISPLAY.colors.secondary, "  -h, --help           Show this help message")
    print_colored(DISPLAY.colors.secondary, "  -v, --verbose        Enable verbose output")
    print_colored(DISPLAY.colors.secondary, "  -q, --quiet          Reduce output verbosity")
    print_colored(DISPLAY.colors.secondary, "  --auto-connect       Automatically connect after successful crack")
    print_colored(DISPLAY.colors.secondary, "  --min-power N        Set minimum signal power threshold (default: 15)")
    print_colored(DISPLAY.colors.secondary, "  --min-acks N         Set minimum ACK packets to collect (default: 7500)")
    print_colored(DISPLAY.colors.secondary, "  --max-acks N         Set maximum ACK packets to collect (default: 15000)")
    print_colored(DISPLAY.colors.secondary, "  --no-save            Disable result logging")
    print("")
    print_colored(DISPLAY.colors.info, "EXAMPLES:")
    print_colored(DISPLAY.colors.secondary, "  waifup3n --verbose --auto-connect")
    print_colored(DISPLAY.colors.secondary, "  waifup3n --min-power 25 --min-acks 10000")
    print("")
end function

// Main Application Logic
main_application = function()
    // Parse command line arguments
    parse_command_line()
    
    // Display startup sequence
    display_banner()
    if CONFIG.VERBOSE then display_hacker_art()
    
    log_info("WAIFUP3N v" + globals.WAIFUP3N_VERSION + " initializing...")
    log_info("Configuration loaded - MIN_POWER: " + CONFIG.MIN_POWER + "%, MIN_ACKS: " + CONFIG.MIN_ACKS)
    
    // Initialize network interfaces
    wifi_interfaces = get_wifi_interfaces()
    if len(wifi_interfaces) == 0 then
        log_error("No WiFi interfaces available - attack cannot proceed")
        exit()
    end if
    
    selected_interface = select_interface(wifi_interfaces)
    log_success("Using interface: " + selected_interface)
    
    // Scan for networks
    raw_networks = scan_networks(selected_interface)
    if len(raw_networks) == 0 then
        log_error("No networks found - ensure you're in range of WiFi access points")
        exit()
    end if
    
    // Parse and analyze networks
    parsed_networks = parse_network_data(raw_networks)
    
    // Filter by minimum power requirement
    filtered_networks = []
    for network in parsed_networks
        if network.power >= CONFIG.MIN_POWER then
            filtered_networks.push(network)
        end if
    end for
    
    if len(filtered_networks) == 0 then
        log_error("No networks meet minimum power requirements (" + CONFIG.MIN_POWER + "%)")
        log_info("Consider lowering --min-power threshold")
        exit()
    end if
    
    log_success("Found " + len(filtered_networks) + " viable targets")
    
    // Target selection
    target = select_target(filtered_networks)
    if not target then exit()
    
    // Execute attack sequence
    log_info("Initiating attack sequence on " + target.essid)
    
    try_count = 0
    max_tries = CONFIG.MAX_RETRIES
    success = false
    password = null
    
    while try_count < max_tries and not success
        try_count = try_count + 1
        if try_count > 1 then
            log_info("Attack attempt " + try_count + " of " + max_tries)
        end if
        
        // Enable monitor mode
        monitor_mode_control(selected_interface, "start")
        
        // Capture handshake
        capture_result = execute_handshake_capture(target, selected_interface)
        
        // Disable monitor mode
        monitor_mode_control(selected_interface, "stop")
        
        // Attempt password crack
        password = execute_password_crack(target)
        
        if password then
            success = true
            break
        else if try_count < max_tries then
            log_warning("Attempt failed, retrying in 5 seconds...")
            wait(5)
        end if
    end while
    
// Save results
    save_attack_results(target, password, success)

    if success then
        attempt_connection(selected_interface, target, password)
    end if

    log_info("WAIFUP3N operation concluded.")
    print_colored(DISPLAY.colors.primary, "═" * 80)
    end function

// Execute the main application
main_application()