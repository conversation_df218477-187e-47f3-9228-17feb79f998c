import_code("/lib/croco_lib.src")


I = new Inputer


P = new Printer

P.settings.wait_after_author_logo = 0

P.settings.wait_after_program_logo = 4


P.settings.program_logo.push(" __     __     __     ______   __     ______   ______")

P.settings.program_logo.push("/\ \  _ \ \   /\ \   /\  ___\ /\ \   /\__  _\ /\  ___\   ")

P.settings.program_logo.push("\ \ \/ \ \ \  \ \ \  \ \  __\ \ \ \  \/_/\ \/ \ \  __\   ")

P.settings.program_logo.push(" \ \__/ \ \_\  \ \_\  \ \_\    \ \_\    \ \_\  \ \_____\ ")

P.settings.program_logo.push("  \/_/   \/_/   \/_/   \/_/     \/_/     \/_/   \/_____/ ")



crypto = lib_import("crypto.so")


P.author_logo

P.program_logo


shell = get_shell

computer = shell.host_computer


P.info("Searching net card")

net_cards = computer.network_devices

if len(net_cards) == 0 then P.error("No net cards found")

net_card = net_cards.split(" ")[0]

P.result("Net card: " + net_card, 1)


P.info("Searching WiFi networks")

networks = computer.wifi_networks(net_card)

if len(networks) == 0 then P.error("No networks found")

P.result("Networks found: " + len(networks), 1)


network = networks[I.option_choice(networks, "network", "BSSID Power ESSID(Name)")]

bssid = network.split(" ")[0]

essid = network.split(" ")[2]

P.info("Target: " + essid)


P.info("Enable mon mode for " + net_card)

crypto.airmon("start", net_card)


P.info("Collecting 7500 ACKs")

P.warning("Do not interrupt the execution! The collection will stop automatically when there are enough ACKs", 1)

crypto.aireplay(bssid, essid, 7500)


P.info("Stopign mon mode for " + net_card)

crypto.airmon("stop", net_card)


P.info("Cracking the password...")

cap = computer.File("file.cap")

password = crypto.aircrack(cap.path)

cap.delete()


if not password then P.error("Fail... Please, try again")


P.result("Success!", 1)

print("WiFi: " + essid)

print("Password: " + password + "\n")


if I.binary("Connect to this network automaticly ?") then computer.connect_wifi(net_card, bssid, essid, password)


/A class for various outputs to the console

Printer = {}


//Defining the settings

Printer.settings = {}


//Color settings

Printer.settings.colors = {}

Printer.settings.colors.info            = "blue"

Printer.settings.colors.error           = "red"

Printer.settings.colors.result          = "green"

Printer.settings.colors.warning         = "yellow"


Printer.settings.colors.author_logo     = "red"

Printer.settings.colors.program_logo    = "white"


//Defining how many seconds to wait after the author logo is displayed

Printer.settings.wait_after_author_logo  = 0

Printer.settings.wait_after_program_logo = 0


//Defining author logo

Printer.settings.author_logo = []

print()

Printer.settings.author_logo.push("               _.---._     .---.")

Printer.settings.author_logo.push("      __...---' .---. `---'-.   `.")

Printer.settings.author_logo.push("  .-''__.--' _.'( | )`.  `.  `._ :")

Printer.settings.author_logo.push(".'__-'_ .--'' ._`---'_.-.  `.   `-`.")

Printer.settings.author_logo.push("       ~ -._ -._``---. -.    `-._   `.")

Printer.settings.author_logo.push("            ~ -.._ _ _ _ ..-_ `.  `-._``--.._")

Printer.settings.author_logo.push("                         -~ -._  `-.  -. `-._``--.._.--''.")

Printer.settings.author_logo.push(" RedCroco soft                ~ ~-.__     -._  `-.__   `. `.")

Printer.settings.author_logo.push("                                    ~~ ~---...__ _    ._ .`")


//Defining program logo (only array in library)

Printer.settings.program_logo = []


//Defining functions:


    // Just print

Printer.p = function(text)

    print(text)

end function


    // Print colored line

Printer.color = function(color, text)

    print("<color=" + color + ">" + text + "</color>")

end function


    // Print "------------------------------""

Printer.frame = function

    print("-"*30)

end function


Printer.space = function(indents = 1)

    if indents == 1 or indents == 0 then

        print("")

    else if indents > 1 then

        print("\n" * (indents - 1))

    else

        P.warning("Can not do " + indents + " indents")

    end if

end function


    //[INFO/WARNING/ERROR/RUSULT] prints:

    //indents - how many "\n" to insert after the line


Printer.info = function(text, indents=0)

    print("<color=" + self.settings.colors.info + ">[INFO]</color> " + text + "..." + ("\n" * indents))

end function


Printer.warning = function(text, indents=0)

    print("<color=" + self.settings.colors.warning + ">[WARNING]</color> " + text + "!" + ("\n" * indents))

end function


Printer.error = function(text, stop=true, indents=0)

    // If you dont need to stop running the program after error messages, then use:

    // Printer.error("some error", false) - print error message and exit


    //if you want to indents, then in any case you will have to manually specify "stop". Unfortunately, this is how this language works

    // Printer.error("some error", true/false, 1) - print error message, make 2 indents and exit


    print("<color=" + self.settings.colors.error + ">[ERROR]</color> " + text + "!" + ("\n" * indents))

    if stop then exit()

end function


Printer.result = function(text, indents=0)

    print("<color=" + self.settings.colors.result + ">[RESULT]</color> " + text + ("\n" * indents))

end function


    //Print author logo

Printer.author_logo = function

    for logo_line in self.settings.author_logo

        self.color(self.settings.colors.author_logo, logo_line)

    end for


    if self.settings.wait_after_author_logo then wait(self.settings.wait_after_author_logo)

end function


//Print program logo

Printer.program_logo = function

    for logo_line in self.settings.program_logo

        self.color(self.settings.colors.program_logo, logo_line)

    end for


    if self.settings.wait_after_program_logo then wait(self.settings.wait_after_program_logo)

end function







//A class for various inputs in the console

Inputer = {}


Inputer.i = function(text)

    // Just input function


    return user_input(text)

end function


Inputer.option_choice = function(choices, what_choice, header)

    //A function for selecting a numbered option from a list.

    //When selecting an option, the countdown starts from 1, but returns the result as if the countdown was from 0 (the user entered 3, returns number 2).


    //choices - array with options to choose from

    //what_choice - the function writes "Enter {what_choice} number from 1 to..."

    //header - what the program will output before the list of choices. "Num" inserts automatically at the beginning of the header


    //Use example: network_number = Inputer.option_choice(networks, "network", "BSSID Power ESSID(Name)")

    //Then output:


    //Num  BSSID              Power  ESSID(Name)

    //1    55:E2:D8:2D:1D:B2  13%    Athmarke_99QRO

    //2    30:04:33:55:83:E0  56%    Lephoenix

    //3    72:7E:E5:5B:EE:B7  63%    Scott

    //4    AB:23:B7:C3:49:93  23%    Dardinal_TBO

    //5    16:C9:7B:8A:01:DB  49%    Greys

    //Enter network number from 1 to 5:


    choices_count = len(choices)

    if choices_count == 0 then exit("choices array is empty")


	to_print = "Num" + " " + header + "\n"

	for choice_index in range(1, choices_count)

		to_print = to_print + choice_index + " " + choices[choice_index - 1] + "\n"

	end for

	print(format_columns(to_print))



	choice = 0

	while choice < 1 or choice > choices_count

		choice = user_input("Enter " + what_choice + " number from 1 to " + choices_count + ": ").val

	end while

	return choice-1

end function


Inputer.binary = function(question, default=true)

    //Sets a binary question.

    //default - responsible for which answer will be highlighted (a large letter) and for what the answer will be if the user does not enter anything


	y = "y"

	n = "n"

	if default then y = "Y"

	if not default then n = "N"



	choice = null

	while true

		choice = user_input(question + "(" + [y,n].join("/") + "):").lower()

		if choice == "y" then return true

        if choice == "n" then return false

        if choice == "" then return default

	end while

end function




// Just functions


lib_import = function(libname)

	//The function tries to import libraries from /lib

	//or from the current path. Return lib if success.


	//Use example: crypto = lib_import('crypto.so')



	lib = include_lib("/lib/" + libname)

	if not lib then lib = include_lib(current_path + "/" + libname)

	if not lib then exit("Missing " + libname + " library")

	return lib

end function
