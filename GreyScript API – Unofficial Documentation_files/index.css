html {
  background: #1e1e1e;
  color: #CCC;
  font-family: monospace;
}

#root {
  overflow-x: hidden;
}

.navigation {
  background: #1e1e1e;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 10px;
  z-index: 999;
}

.navigation .search {
  position: relative;
  width: 100%;
}

.navigation .search .search-input {
  border: none;
  -webkit-appearance: none;
  -ms-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #f2f2f2;
  padding: 12px 2%;
  border-radius: 3px;
  height: 9px;
  width: 96%;
  font-size: 14px;
}

.navigation .search .clear {
  cursor: pointer;
  color: #000;
  position: absolute;
  z-index: 999;
  right: 5px;
  font-size: 20px;
  top: 5px;
}

.navigation .search .tags-input a {
  color: #FFF;
  text-decoration: none;
  cursor: pointer;
  display: inline-block;
  padding: 3px;
  margin: 2px;
  background: #3dc9b0;
  font-size: 10px;
}

.navigation .search .tags-input a.active {
  background: #aa0a0a;
}

.navigation .search .clear:before {
  content: "\e5cd";
}

.readme {
  margin: 115px 0 0;
}

.readme .intro h1 {
  margin: 0.67em 10px;
  color: #ce9178;
}

.readme .intro article {
  margin: 0.67em 10px;
}

.readme .intro article a {
  color: #fff;
}

.readme .github-button {
  position: none;
  top: 0;
  right: 0;
  padding: 5px;
  z-index: 999;
}

.readme .external-links-wrapper {
  margin: 0;
  padding: 0;
  right: 5px;
  bottom: 5px;
  position: fixed;
  z-index: 999;
  background: #333;
  opacity: 0.7;
}

.readme .external-links-wrapper li {
  margin: 0;
  padding: 0;
  list-style: none;
  display: none;
}

.readme .external-links-wrapper .collapse {
  cursor: pointer;
  padding: 0.7em 1.4em;
}

.editor {
  height: 100px !important;
  width: auto !important;
  border: 1px groove #333 !important;
  margin: 0 !important;
  padding: 5px !important;
  font-size: 12px !important;
  overflow: hidden;
}

.token.type,
.token.class {
  color: #3dc9b0 !important;
}

.token.number {
  color: #b5cea8 !important;
}

.token.string {
  color: #ce9178 !important;
}

.token.method {
  color: #dcdcaa !important;
}

.token.keyword {
  color: #c586c0 !important;
}

.editorWrapper {
  position: relative;
}

.editorWrapper .run {
  position: absolute;
  bottom: 5px;
  right: 5px;
  display: inline-block;
  padding: 5px;
  border-radius: 0.15em;
  box-sizing: border-box;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 400;
  color: #ffffff;
  background-color: #03b140;
  box-shadow: inset 0 -0.6em 0 -0.35em rgb(0 0 0 / 17%);
  text-align: center;
  cursor: pointer;
  border: 1px solid transparent;
  z-index: 998;
}

.editorWrapper .run:before {
  content: "\eb8e";
}

.editorWrapper .run:active {
  border: 1px solid #000;
}

.content-table {
  overflow: hidden;
  max-height: 100px;
  margin: 10px 0 0;
  min-height: 34px;
  position: relative;
}

.content-table .collapse {
  font-size: 30px;
  border: 1px solid;
  position: absolute;
  top: 0;
  left: 0;
}

.content-table .collapse:before {
  content: "\e5d2";
}

.content-table .collapse.active {
  color: #ce9178;
}

.content-table>div {
  margin: 0 0 0 36px;
  overflow: auto;
  height: 100px;
  scrollbar-color: #CCC #333;
  scrollbar-width: thin;
}

.content-table::-webkit-scrollbar-thumb,
.content-table>div::-webkit-scrollbar-thumb {
  background-color: #CCC;
}

.content-table::-webkit-scrollbar-thumb,
.content-table>div::-webkit-scrollbar-track {
  background-color: #333;
}

.content-table::-webkit-scrollbar-thumb,
.content-table>div::-webkit-scrollbar-corner {
  background-color: #333;
}

.content-table::-webkit-scrollbar-thumb,
.content-table>div::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}

.content-table>.hidden {
  display: none;
}

.content-table ul {
  margin: 0;
  padding: 0;
}

.content-table li {
  margin: 0;
  padding: 0;
  list-style: none;
}

.content-table .first {
  margin: 0 0 0 10px;
}

.content-table .first>li {
  clear: both;
}

.content-table .first>li>a {
  color: #3dc9b0;
  text-transform: capitalize;
}

.content-table .second {
  margin: 0 0 10px 10px;
}

.content-table .second>li {
  float: left;
  margin: 0 10px 0 0;
}

.content-table .second>li:last-child:after {
  content: '';
}

.content-table .second>li:after {
  content: '-';
}

.content-table .second>li>a {
  color: #74b0df;
  margin: 0 10px 0 0;
}

.content-table .second>li:last-child>a {
  margin: 0;
}

.content-table a {
  text-decoration: none;
  color: #CCC;
  cursor: pointer;
}

.basics>a {
  right: 5px;
  bottom: 5px;
  position: fixed;
  z-index: 999;
  display: inline-block;
  padding: 0.7em 1.4em;
  margin: 0;
  border-radius: 0.15em;
  box-sizing: border-box;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 400;
  color: #333;
  background-color: #999;
  box-shadow: inset 0 -0.6em 0 -0.35em rgb(0 0 0 / 17%);
  text-align: center;
  cursor: pointer;
  border: 1px solid transparent;
}

.basics>a:active {
  border: 1px solid #000;
}

.basics>div {
  border: 1px groove #333;
  padding: 10px;
  margin: 0 0 10px;
}

.basics .editorWrapper {
  margin: 10px 0 0;
}

.basics h2 {
  color: #3dc9b0;
}

.collapse {
  cursor: pointer;
}

.basics .hidden {
  display: none;
}

.definitions ul,
.definitions p {
  margin: 0;
  padding: 0;
  word-break: break-word;
}

.definitions code[class*=language-] {
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.definitions a {
  color: #FFF;
  cursor: pointer;
}

.definitions p {
  font-size: 14px;
}

.definitions li {
  margin: 0;
  padding: 0;
  list-style: none;
}

.definitions li>p {
  margin: 10px 0;
}

.definitions .hidden {
  display: none;
}

.definitions .first {
  border: 1px groove #333;
}

.definitions .first>li:first-child {
  margin: 10px;
}

.definitions .first>li {
  margin: 20px 10px;
}

.definitions .second {
  margin: 15px 0 0 10px;
  border-top: 1px groove #333;
}

.definitions .second>li {
  border-bottom: 1px groove #333;
  padding: 10px;
  margin: 0;
}

.definitions h2 {
  color: #3dc9b0;
  text-transform: capitalize;
  position: relative;
}

.definitions .definition {
  position: relative;
}

.definitions .definition .share {
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  color: #ce9178;
}

.definitions .definition h3 {
  margin: 0;
  color: #74b0df;
}

.definitions .definition .signature {
  font-size: 14px;
}

.definitions .definition .signature .args,
.definitions .definition .returns {
  display: inline-block;
}

.definitions .definition .signature .args .label {
  color: #d4d4d4;
}

.definitions .definition .signature .args .type {
  color: #3dc9b0;
}

.definitions .definition .signature .args .default .string {
  color: #ce9178;
}

.definitions .definition .signature .args .default .number {
  color: #b5cea8;
}

.definitions .definition .signature .args .default .boolean {
  color: #569cd6;
}

.definitions .definition .signature .returns .type {
  color: #3dc9b0;
}

.definitions .definition .signature .returns .or,
.definitions .definition .signature .args .or {
  margin: 0 5px;
  color: #569cd6;
}

.definitions .definition .variations .info {
  font-size: 15px;
  bottom: 2px;
  position: relative;
  color: #ce9178;
  cursor: pointer;
}

.definitions .definition .variations .info:before {
  content: "\e88e";
}

.definitions .definition .variations:hover .variations-wrapper {
  display: block;
}

.definitions .definition .variations .variations-wrapper {
  display: none;
  position: absolute;
  background: #1e1e1e;
  opacity: 0.95;
  border: 1px solid #FFF;
  padding: 5px;
  z-index: 999;
}

.definitions .definition .variations .variation {
  display: block;
  font-size: 14px;
}

.definitions .definition .variations .variation.string {
  color: #ce9178 !important;
}

.definitions .definition .variations .variation.number {
  color: #b5cea8 !important;
}

.definitions .definition .description {
  font-size: 14px;
  margin: 10px 0 0;
}

.definitions .definition .description li {
  margin: 5px 0 0 20px;
  list-style: square;
}

.definitions .definition .example {
  margin: 10px 0 0;
}

@media screen and (min-width: 760px) {
  .navigation {
    padding: 0;
    width: 235px;
  }

  .navigation .search {
    width: 100%;
  }

  .navigation .search .clear {
    position: absolute;
    top: 16px;
    right: 8px;
  }

  .navigation .search .search-input {
    position: fixed;
    left: 0;
    top: 0;
    padding: 12px;
    height: 9px;
    width: 100%;
    margin: 10px;
    z-index: 999;
    width: 200px;
  }

  .navigation .search .tags-input {
    position: fixed;
    left: 0;
    top: 40px;
    height: 9px;
    width: 100%;
    margin: 10px;
    z-index: 999;
    width: 200px;
  }

  .readme {
    margin: 0;
  }

  .readme .external-links-wrapper li {
    display: inline-block;
  }

  .readme .github-button {
    position: fixed;
  }

  .readme .external-links-wrapper .external-links a {
    display: inline-block;
    padding: 0.7em 1.4em;
    margin: 0 0 0 0.3em;
    border-radius: 0.15em;
    box-sizing: border-box;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: bold;
    color: #fff;
    text-align: center;
    position: relative;
    cursor: pointer;
  }

  .readme .external-links-wrapper a:active {
    border: 1px solid #000;
  }

  .content-wrapper {
    margin: 0 0 0 235px;
  }

  .content-table {
    margin: 0;
    max-height: none;
    top: 100px;
    bottom: 0;
    left: 0;
    position: fixed;
    width: 235px;
    overflow: auto;
    -ms-overflow-style: auto;
    scrollbar-color: #CCC #333;
    scrollbar-width: thin;
  }

  .content-table .collapse {
    display: none;
  }

  .content-table>div {
    margin: 0;
    height: auto;
  }

  .content-table>.hidden {
    display: block;
  }

  .content-table .second>li {
    float: none;
    margin: 0;
  }

  .content-table .second>li:after {
    content: '';
  }

  .content-table .second>li>a {
    margin: 0;
  }
}