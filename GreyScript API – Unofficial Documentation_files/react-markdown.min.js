(function (g, f) {typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = f() : typeof define === 'function' && define.amd ? define([], f) : (g = typeof globalThis !== 'undefined' ? globalThis : g || self, g.ReactMarkdown = f()); }(this, (function () { 'use strict';
"use strict";var ReactMarkdown=(()=>{var Go=Object.create;var tn=Object.defineProperty,Zo=Object.defineProperties,Jo=Object.getOwnPropertyDescriptor,el=Object.getOwnPropertyDescriptors,nl=Object.getOwnPropertyNames,fr=Object.getOwnPropertySymbols,tl=Object.getPrototypeOf,hr=Object.prototype.hasOwnProperty,rl=Object.prototype.propertyIsEnumerable;var dr=Math.pow,mr=(e,n,t)=>n in e?tn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,pe=(e,n)=>{for(var t in n||(n={}))hr.call(n,t)&&mr(e,t,n[t]);if(fr)for(var t of fr(n))rl.call(n,t)&&mr(e,t,n[t]);return e},Ve=(e,n)=>Zo(e,el(n));var ye=(e,n)=>()=>(n||e((n={exports:{}}).exports,n),n.exports),tt=(e,n)=>{for(var t in n)tn(e,t,{get:n[t],enumerable:!0})},gr=(e,n,t,r)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of nl(n))!hr.call(e,i)&&i!==t&&tn(e,i,{get:()=>n[i],enumerable:!(r=Jo(n,i))||r.enumerable});return e};var Pe=(e,n,t)=>(t=e!=null?Go(tl(e)):{},gr(n||!e||!e.__esModule?tn(t,"default",{value:e,enumerable:!0}):t,e)),il=e=>gr(tn({},"__esModule",{value:!0}),e);var Fr=ye(_=>{"use strict";var rn=Symbol.for("react.element"),ol=Symbol.for("react.portal"),ll=Symbol.for("react.fragment"),ul=Symbol.for("react.strict_mode"),al=Symbol.for("react.profiler"),sl=Symbol.for("react.provider"),cl=Symbol.for("react.context"),pl=Symbol.for("react.forward_ref"),fl=Symbol.for("react.suspense"),ml=Symbol.for("react.memo"),hl=Symbol.for("react.lazy"),xr=Symbol.iterator;function dl(e){return e===null||typeof e!="object"?null:(e=xr&&e[xr]||e["@@iterator"],typeof e=="function"?e:null)}var wr={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Sr=Object.assign,Er={};function $e(e,n,t){this.props=e,this.context=n,this.refs=Er,this.updater=t||wr}$e.prototype.isReactComponent={};$e.prototype.setState=function(e,n){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")};$e.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Cr(){}Cr.prototype=$e.prototype;function it(e,n,t){this.props=e,this.context=n,this.refs=Er,this.updater=t||wr}var ot=it.prototype=new Cr;ot.constructor=it;Sr(ot,$e.prototype);ot.isPureReactComponent=!0;var kr=Array.isArray,Ar=Object.prototype.hasOwnProperty,lt={current:null},Pr={key:!0,ref:!0,__self:!0,__source:!0};function Ir(e,n,t){var r,i={},o=null,l=null;if(n!=null)for(r in n.ref!==void 0&&(l=n.ref),n.key!==void 0&&(o=""+n.key),n)Ar.call(n,r)&&!Pr.hasOwnProperty(r)&&(i[r]=n[r]);var u=arguments.length-2;if(u===1)i.children=t;else if(1<u){for(var a=Array(u),f=0;f<u;f++)a[f]=arguments[f+2];i.children=a}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)i[r]===void 0&&(i[r]=u[r]);return{$$typeof:rn,type:e,key:o,ref:l,props:i,_owner:lt.current}}function gl(e,n){return{$$typeof:rn,type:e.type,key:n,ref:e.ref,props:e.props,_owner:e._owner}}function ut(e){return typeof e=="object"&&e!==null&&e.$$typeof===rn}function yl(e){var n={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(t){return n[t]})}var br=/\/+/g;function rt(e,n){return typeof e=="object"&&e!==null&&e.key!=null?yl(""+e.key):n.toString(36)}function yn(e,n,t,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(o){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case rn:case ol:l=!0}}if(l)return l=e,i=i(l),e=r===""?"."+rt(l,0):r,kr(i)?(t="",e!=null&&(t=e.replace(br,"$&/")+"/"),yn(i,n,t,"",function(f){return f})):i!=null&&(ut(i)&&(i=gl(i,t+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(br,"$&/")+"/")+e)),n.push(i)),1;if(l=0,r=r===""?".":r+":",kr(e))for(var u=0;u<e.length;u++){o=e[u];var a=r+rt(o,u);l+=yn(o,n,t,a,i)}else if(a=dl(e),typeof a=="function")for(e=a.call(e),u=0;!(o=e.next()).done;)o=o.value,a=r+rt(o,u++),l+=yn(o,n,t,a,i);else if(o==="object")throw n=String(e),Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.");return l}function gn(e,n,t){if(e==null)return e;var r=[],i=0;return yn(e,r,"","",function(o){return n.call(t,o,i++)}),r}function xl(e){if(e._status===-1){var n=e._result;n=n(),n.then(function(t){(e._status===0||e._status===-1)&&(e._status=1,e._result=t)},function(t){(e._status===0||e._status===-1)&&(e._status=2,e._result=t)}),e._status===-1&&(e._status=0,e._result=n)}if(e._status===1)return e._result.default;throw e._result}var le={current:null},xn={transition:null},kl={ReactCurrentDispatcher:le,ReactCurrentBatchConfig:xn,ReactCurrentOwner:lt};_.Children={map:gn,forEach:function(e,n,t){gn(e,function(){n.apply(this,arguments)},t)},count:function(e){var n=0;return gn(e,function(){n++}),n},toArray:function(e){return gn(e,function(n){return n})||[]},only:function(e){if(!ut(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};_.Component=$e;_.Fragment=ll;_.Profiler=al;_.PureComponent=it;_.StrictMode=ul;_.Suspense=fl;_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=kl;_.cloneElement=function(e,n,t){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Sr({},e.props),i=e.key,o=e.ref,l=e._owner;if(n!=null){if(n.ref!==void 0&&(o=n.ref,l=lt.current),n.key!==void 0&&(i=""+n.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in n)Ar.call(n,a)&&!Pr.hasOwnProperty(a)&&(r[a]=n[a]===void 0&&u!==void 0?u[a]:n[a])}var a=arguments.length-2;if(a===1)r.children=t;else if(1<a){u=Array(a);for(var f=0;f<a;f++)u[f]=arguments[f+2];r.children=u}return{$$typeof:rn,type:e.type,key:i,ref:o,props:r,_owner:l}};_.createContext=function(e){return e={$$typeof:cl,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:sl,_context:e},e.Consumer=e};_.createElement=Ir;_.createFactory=function(e){var n=Ir.bind(null,e);return n.type=e,n};_.createRef=function(){return{current:null}};_.forwardRef=function(e){return{$$typeof:pl,render:e}};_.isValidElement=ut;_.lazy=function(e){return{$$typeof:hl,_payload:{_status:-1,_result:e},_init:xl}};_.memo=function(e,n){return{$$typeof:ml,type:e,compare:n===void 0?null:n}};_.startTransition=function(e){var n=xn.transition;xn.transition={};try{e()}finally{xn.transition=n}};_.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};_.useCallback=function(e,n){return le.current.useCallback(e,n)};_.useContext=function(e){return le.current.useContext(e)};_.useDebugValue=function(){};_.useDeferredValue=function(e){return le.current.useDeferredValue(e)};_.useEffect=function(e,n){return le.current.useEffect(e,n)};_.useId=function(){return le.current.useId()};_.useImperativeHandle=function(e,n,t){return le.current.useImperativeHandle(e,n,t)};_.useInsertionEffect=function(e,n){return le.current.useInsertionEffect(e,n)};_.useLayoutEffect=function(e,n){return le.current.useLayoutEffect(e,n)};_.useMemo=function(e,n){return le.current.useMemo(e,n)};_.useReducer=function(e,n,t){return le.current.useReducer(e,n,t)};_.useRef=function(e){return le.current.useRef(e)};_.useState=function(e){return le.current.useState(e)};_.useSyncExternalStore=function(e,n,t){return le.current.useSyncExternalStore(e,n,t)};_.useTransition=function(){return le.current.useTransition()};_.version="18.2.0"});var at=ye((Ya,Or)=>{"use strict";Or.exports=Fr()});var st=ye((Ka,Tr)=>{Tr.exports=function(n){return n!=null&&n.constructor!=null&&typeof n.constructor.isBuffer=="function"&&n.constructor.isBuffer(n)}});var $r=ye((xs,Vr)=>{"use strict";var kn=Object.prototype.hasOwnProperty,qr=Object.prototype.toString,Br=Object.defineProperty,Nr=Object.getOwnPropertyDescriptor,Mr=function(n){return typeof Array.isArray=="function"?Array.isArray(n):qr.call(n)==="[object Array]"},jr=function(n){if(!n||qr.call(n)!=="[object Object]")return!1;var t=kn.call(n,"constructor"),r=n.constructor&&n.constructor.prototype&&kn.call(n.constructor.prototype,"isPrototypeOf");if(n.constructor&&!t&&!r)return!1;var i;for(i in n);return typeof i=="undefined"||kn.call(n,i)},Ur=function(n,t){Br&&t.name==="__proto__"?Br(n,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):n[t.name]=t.newValue},Hr=function(n,t){if(t==="__proto__")if(kn.call(n,t)){if(Nr)return Nr(n,t).value}else return;return n[t]};Vr.exports=function e(){var n,t,r,i,o,l,u=arguments[0],a=1,f=arguments.length,s=!1;for(typeof u=="boolean"&&(s=u,u=arguments[1]||{},a=2),(u==null||typeof u!="object"&&typeof u!="function")&&(u={});a<f;++a)if(n=arguments[a],n!=null)for(t in n)r=Hr(u,t),i=Hr(n,t),u!==i&&(s&&i&&(jr(i)||(o=Mr(i)))?(o?(o=!1,l=r&&Mr(r)?r:[]):l=r&&jr(r)?r:{},Ur(u,{name:t,newValue:e(s,l,i)})):typeof i!="undefined"&&Ur(u,{name:t,newValue:i}));return u}});var co=ye((Nh,so)=>{"use strict";var ga="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";so.exports=ga});var ho=ye((Mh,mo)=>{"use strict";var ya=co();function po(){}function fo(){}fo.resetWarningCache=po;mo.exports=function(){function e(r,i,o,l,u,a){if(a!==ya){var f=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw f.name="Invariant Violation",f}}e.isRequired=e;function n(){return e}var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:n,element:e,elementType:e,instanceOf:n,node:e,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:fo,resetWarningCache:po};return t.PropTypes=t,t}});var yo=ye((Hh,go)=>{go.exports=ho()();var jh,Uh});var Ao=ye(U=>{"use strict";var er=Symbol.for("react.element"),nr=Symbol.for("react.portal"),Un=Symbol.for("react.fragment"),Hn=Symbol.for("react.strict_mode"),qn=Symbol.for("react.profiler"),Vn=Symbol.for("react.provider"),$n=Symbol.for("react.context"),Ca=Symbol.for("react.server_context"),Wn=Symbol.for("react.forward_ref"),Qn=Symbol.for("react.suspense"),Xn=Symbol.for("react.suspense_list"),Yn=Symbol.for("react.memo"),Kn=Symbol.for("react.lazy"),Aa=Symbol.for("react.offscreen"),Co;Co=Symbol.for("react.module.reference");function he(e){if(typeof e=="object"&&e!==null){var n=e.$$typeof;switch(n){case er:switch(e=e.type,e){case Un:case qn:case Hn:case Qn:case Xn:return e;default:switch(e=e&&e.$$typeof,e){case Ca:case $n:case Wn:case Kn:case Yn:case Vn:return e;default:return n}}case nr:return n}}}U.ContextConsumer=$n;U.ContextProvider=Vn;U.Element=er;U.ForwardRef=Wn;U.Fragment=Un;U.Lazy=Kn;U.Memo=Yn;U.Portal=nr;U.Profiler=qn;U.StrictMode=Hn;U.Suspense=Qn;U.SuspenseList=Xn;U.isAsyncMode=function(){return!1};U.isConcurrentMode=function(){return!1};U.isContextConsumer=function(e){return he(e)===$n};U.isContextProvider=function(e){return he(e)===Vn};U.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===er};U.isForwardRef=function(e){return he(e)===Wn};U.isFragment=function(e){return he(e)===Un};U.isLazy=function(e){return he(e)===Kn};U.isMemo=function(e){return he(e)===Yn};U.isPortal=function(e){return he(e)===nr};U.isProfiler=function(e){return he(e)===qn};U.isStrictMode=function(e){return he(e)===Hn};U.isSuspense=function(e){return he(e)===Qn};U.isSuspenseList=function(e){return he(e)===Xn};U.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Un||e===qn||e===Hn||e===Qn||e===Xn||e===Aa||typeof e=="object"&&e!==null&&(e.$$typeof===Kn||e.$$typeof===Yn||e.$$typeof===Vn||e.$$typeof===$n||e.$$typeof===Wn||e.$$typeof===Co||e.getModuleId!==void 0)};U.typeOf=he});var Io=ye((jd,Po)=>{"use strict";Po.exports=Ao()});var _o=ye((Vd,Ro)=>{var Lo=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,Pa=/\n/g,Ia=/^\s*/,Fa=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,Oa=/^:\s*/,Ta=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,La=/^[;\s]*/,va=/^\s+|\s+$/g,Da=`
`,vo="/",Do="*",Ue="",za="comment",Ra="declaration";Ro.exports=function(e,n){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];n=n||{};var t=1,r=1;function i(b){var w=b.match(Pa);w&&(t+=w.length);var y=b.lastIndexOf(Da);r=~y?b.length-y:r+b.length}function o(){var b={line:t,column:r};return function(w){return w.position=new l(b),s(),w}}function l(b){this.start=b,this.end={line:t,column:r},this.source=n.source}l.prototype.content=e;var u=[];function a(b){var w=new Error(n.source+":"+t+":"+r+": "+b);if(w.reason=b,w.filename=n.source,w.line=t,w.column=r,w.source=e,n.silent)u.push(w);else throw w}function f(b){var w=b.exec(e);if(w){var y=w[0];return i(y),e=e.slice(y.length),w}}function s(){f(Ia)}function d(b){var w;for(b=b||[];w=m();)w!==!1&&b.push(w);return b}function m(){var b=o();if(!(vo!=e.charAt(0)||Do!=e.charAt(1))){for(var w=2;Ue!=e.charAt(w)&&(Do!=e.charAt(w)||vo!=e.charAt(w+1));)++w;if(w+=2,Ue===e.charAt(w-1))return a("End of comment missing");var y=e.slice(2,w-2);return r+=2,i(y),e=e.slice(w),r+=2,b({type:za,comment:y})}}function h(){var b=o(),w=f(Fa);if(w){if(m(),!f(Oa))return a("property missing ':'");var y=f(Ta),v=b({type:Ra,property:zo(w[0].replace(Lo,Ue)),value:y?zo(y[0].replace(Lo,Ue)):Ue});return f(La),v}}function k(){var b=[];d(b);for(var w;w=h();)w!==!1&&(b.push(w),d(b));return b}return s(),k()};function zo(e){return e?e.replace(va,Ue):Ue}});var No=ye(($d,tr)=>{var _a=_o();function Bo(e,n){var t=null;if(!e||typeof e!="string")return t;for(var r,i=_a(e),o=typeof n=="function",l,u,a=0,f=i.length;a<f;a++)r=i[a],l=r.property,u=r.value,o?n(l,u,r):u&&(t||(t={}),t[l]=u);return t}tr.exports=Bo;tr.exports.default=Bo});var $a={};tt($a,{default:()=>lr,uriTransformer:()=>dn});var yr=["http","https","mailto","tel"];function dn(e){let n=(e||"").trim(),t=n.charAt(0);if(t==="#"||t==="/")return n;let r=n.indexOf(":");if(r===-1)return n;let i=-1;for(;++i<yr.length;){let o=yr[i];if(r===o.length&&n.slice(0,o.length).toLowerCase()===o)return n}return i=n.indexOf("?"),i!==-1&&r>i||(i=n.indexOf("#"),i!==-1&&r>i)?n:"javascript:void(0)"}var Jn=Pe(at(),1);var _r=Pe(st(),1);function Ie(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?Lr(e.position):"start"in e||"end"in e?Lr(e):"line"in e||"column"in e?ct(e):""}function ct(e){return vr(e&&e.line)+":"+vr(e&&e.column)}function Lr(e){return ct(e&&e.start)+"-"+ct(e&&e.end)}function vr(e){return e&&typeof e=="number"?e:1}var te=class extends Error{constructor(n,t,r){let i=[null,null],o={start:{line:null,column:null},end:{line:null,column:null}};if(super(),typeof t=="string"&&(r=t,t=void 0),typeof r=="string"){let l=r.indexOf(":");l===-1?i[1]=r:(i[0]=r.slice(0,l),i[1]=r.slice(l+1))}t&&("type"in t||"position"in t?t.position&&(o=t.position):"start"in t||"end"in t?o=t:("line"in t||"column"in t)&&(o.start=t)),this.name=Ie(t)||"1:1",this.message=typeof n=="object"?n.message:n,this.stack="",typeof n=="object"&&n.stack&&(this.stack=n.stack),this.reason=this.message,this.fatal,this.line=o.start.line,this.column=o.start.column,this.position=o,this.source=i[0],this.ruleId=i[1],this.file,this.actual,this.expected,this.url,this.note}};te.prototype.file="";te.prototype.name="";te.prototype.reason="";te.prototype.message="";te.prototype.stack="";te.prototype.fatal=null;te.prototype.column=null;te.prototype.line=null;te.prototype.source=null;te.prototype.ruleId=null;te.prototype.position=null;var xe={basename:bl,dirname:wl,extname:Sl,join:El,sep:"/"};function bl(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');on(e);let t=0,r=-1,i=e.length,o;if(n===void 0||n.length===0||n.length>e.length){for(;i--;)if(e.charCodeAt(i)===47){if(o){t=i+1;break}}else r<0&&(o=!0,r=i+1);return r<0?"":e.slice(t,r)}if(n===e)return"";let l=-1,u=n.length-1;for(;i--;)if(e.charCodeAt(i)===47){if(o){t=i+1;break}}else l<0&&(o=!0,l=i+1),u>-1&&(e.charCodeAt(i)===n.charCodeAt(u--)?u<0&&(r=i):(u=-1,r=l));return t===r?r=l:r<0&&(r=e.length),e.slice(t,r)}function wl(e){if(on(e),e.length===0)return".";let n=-1,t=e.length,r;for(;--t;)if(e.charCodeAt(t)===47){if(r){n=t;break}}else r||(r=!0);return n<0?e.charCodeAt(0)===47?"/":".":n===1&&e.charCodeAt(0)===47?"//":e.slice(0,n)}function Sl(e){on(e);let n=e.length,t=-1,r=0,i=-1,o=0,l;for(;n--;){let u=e.charCodeAt(n);if(u===47){if(l){r=n+1;break}continue}t<0&&(l=!0,t=n+1),u===46?i<0?i=n:o!==1&&(o=1):i>-1&&(o=-1)}return i<0||t<0||o===0||o===1&&i===t-1&&i===r+1?"":e.slice(i,t)}function El(...e){let n=-1,t;for(;++n<e.length;)on(e[n]),e[n]&&(t=t===void 0?e[n]:t+"/"+e[n]);return t===void 0?".":Cl(t)}function Cl(e){on(e);let n=e.charCodeAt(0)===47,t=Al(e,!n);return t.length===0&&!n&&(t="."),t.length>0&&e.charCodeAt(e.length-1)===47&&(t+="/"),n?"/"+t:t}function Al(e,n){let t="",r=0,i=-1,o=0,l=-1,u,a;for(;++l<=e.length;){if(l<e.length)u=e.charCodeAt(l);else{if(u===47)break;u=47}if(u===47){if(!(i===l-1||o===1))if(i!==l-1&&o===2){if(t.length<2||r!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){if(a=t.lastIndexOf("/"),a!==t.length-1){a<0?(t="",r=0):(t=t.slice(0,a),r=t.length-1-t.lastIndexOf("/")),i=l,o=0;continue}}else if(t.length>0){t="",r=0,i=l,o=0;continue}}n&&(t=t.length>0?t+"/..":"..",r=2)}else t.length>0?t+="/"+e.slice(i+1,l):t=e.slice(i+1,l),r=l-i-1;i=l,o=0}else u===46&&o>-1?o++:o=-1}return t}function on(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}var Dr={cwd:Pl};function Pl(){return"/"}function We(e){return e!==null&&typeof e=="object"&&e.href&&e.origin}function zr(e){if(typeof e=="string")e=new URL(e);else if(!We(e)){let n=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw n.code="ERR_INVALID_ARG_TYPE",n}if(e.protocol!=="file:"){let n=new TypeError("The URL must be of scheme file");throw n.code="ERR_INVALID_URL_SCHEME",n}return Il(e)}function Il(e){if(e.hostname!==""){let r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}let n=e.pathname,t=-1;for(;++t<n.length;)if(n.charCodeAt(t)===37&&n.charCodeAt(t+1)===50){let r=n.charCodeAt(t+2);if(r===70||r===102){let i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(n)}var pt=["history","path","basename","stem","extname","dirname"],ze=class{constructor(n){let t;n?typeof n=="string"||Fl(n)?t={value:n}:We(n)?t={path:n}:t=n:t={},this.data={},this.messages=[],this.history=[],this.cwd=Dr.cwd(),this.value,this.stored,this.result,this.map;let r=-1;for(;++r<pt.length;){let o=pt[r];o in t&&t[o]!==void 0&&t[o]!==null&&(this[o]=o==="history"?[...t[o]]:t[o])}let i;for(i in t)pt.includes(i)||(this[i]=t[i])}get path(){return this.history[this.history.length-1]}set path(n){We(n)&&(n=zr(n)),mt(n,"path"),this.path!==n&&this.history.push(n)}get dirname(){return typeof this.path=="string"?xe.dirname(this.path):void 0}set dirname(n){Rr(this.basename,"dirname"),this.path=xe.join(n||"",this.basename)}get basename(){return typeof this.path=="string"?xe.basename(this.path):void 0}set basename(n){mt(n,"basename"),ft(n,"basename"),this.path=xe.join(this.dirname||"",n)}get extname(){return typeof this.path=="string"?xe.extname(this.path):void 0}set extname(n){if(ft(n,"extname"),Rr(this.dirname,"extname"),n){if(n.charCodeAt(0)!==46)throw new Error("`extname` must start with `.`");if(n.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=xe.join(this.dirname,this.stem+(n||""))}get stem(){return typeof this.path=="string"?xe.basename(this.path,this.extname):void 0}set stem(n){mt(n,"stem"),ft(n,"stem"),this.path=xe.join(this.dirname||"",n+(this.extname||""))}toString(n){return(this.value||"").toString(n||void 0)}message(n,t,r){let i=new te(n,t,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}info(n,t,r){let i=this.message(n,t,r);return i.fatal=null,i}fail(n,t,r){let i=this.message(n,t,r);throw i.fatal=!0,i}};function ft(e,n){if(e&&e.includes(xe.sep))throw new Error("`"+n+"` cannot be a path: did not expect `"+xe.sep+"`")}function mt(e,n){if(!e)throw new Error("`"+n+"` cannot be empty")}function Rr(e,n){if(!e)throw new Error("Setting `"+n+"` requires `path` to be set too")}function Fl(e){return(0,_r.default)(e)}function ht(e){if(e)throw e}var Kr=Pe(st(),1),xt=Pe($r(),1);function ln(e){if(typeof e!="object"||e===null)return!1;let n=Object.getPrototypeOf(e);return(n===null||n===Object.prototype||Object.getPrototypeOf(n)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Wr(){let e=[],n={run:t,use:r};return n;function t(...i){let o=-1,l=i.pop();if(typeof l!="function")throw new TypeError("Expected function as last argument, not "+l);u(null,...i);function u(a,...f){let s=e[++o],d=-1;if(a){l(a);return}for(;++d<i.length;)(f[d]===null||f[d]===void 0)&&(f[d]=i[d]);i=f,s?Ol(s,u)(...f):l(null,...f)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),n}}function Ol(e,n){let t;return r;function r(...l){let u=e.length>l.length,a;u&&l.push(i);try{a=e.apply(this,l)}catch(f){let s=f;if(u&&t)throw s;return i(s)}u||(a instanceof Promise?a.then(o,i):a instanceof Error?i(a):o(a))}function i(l,...u){t||(t=!0,n(l,...u))}function o(l){i(null,l)}}var kt=Zr().freeze(),Gr={}.hasOwnProperty;function Zr(){let e=Wr(),n=[],t={},r,i=-1;return o.data=l,o.Parser=void 0,o.Compiler=void 0,o.freeze=u,o.attachers=n,o.use=a,o.parse=f,o.stringify=s,o.run=d,o.runSync=m,o.process=h,o.processSync=k,o;function o(){let b=Zr(),w=-1;for(;++w<n.length;)b.use(...n[w]);return b.data((0,xt.default)(!0,{},t)),b}function l(b,w){return typeof b=="string"?arguments.length===2?(yt("data",r),t[b]=w,o):Gr.call(t,b)&&t[b]||null:b?(yt("data",r),t=b,o):t}function u(){if(r)return o;for(;++i<n.length;){let[b,...w]=n[i];if(w[0]===!1)continue;w[0]===!0&&(w[0]=void 0);let y=b.call(o,...w);typeof y=="function"&&e.use(y)}return r=!0,i=Number.POSITIVE_INFINITY,o}function a(b,...w){let y;if(yt("use",r),b!=null)if(typeof b=="function")z(b,...w);else if(typeof b=="object")Array.isArray(b)?B(b):C(b);else throw new TypeError("Expected usable value, not `"+b+"`");return y&&(t.settings=Object.assign(t.settings||{},y)),o;function v(x){if(typeof x=="function")z(x);else if(typeof x=="object")if(Array.isArray(x)){let[T,...R]=x;z(T,...R)}else C(x);else throw new TypeError("Expected usable value, not `"+x+"`")}function C(x){B(x.plugins),x.settings&&(y=Object.assign(y||{},x.settings))}function B(x){let T=-1;if(x!=null)if(Array.isArray(x))for(;++T<x.length;){let R=x[T];v(R)}else throw new TypeError("Expected a list of plugins, not `"+x+"`")}function z(x,T){let R=-1,N;for(;++R<n.length;)if(n[R][0]===x){N=n[R];break}N?(ln(N[1])&&ln(T)&&(T=(0,xt.default)(!0,N[1],T)),N[1]=T):n.push([...arguments])}}function f(b){o.freeze();let w=un(b),y=o.Parser;return dt("parse",y),Qr(y,"parse")?new y(String(w),w).parse():y(String(w),w)}function s(b,w){o.freeze();let y=un(w),v=o.Compiler;return gt("stringify",v),Xr(b),Qr(v,"compile")?new v(b,y).compile():v(b,y)}function d(b,w,y){if(Xr(b),o.freeze(),!y&&typeof w=="function"&&(y=w,w=void 0),!y)return new Promise(v);v(null,y);function v(C,B){e.run(b,un(w),z);function z(x,T,R){T=T||b,x?B(x):C?C(T):y(null,T,R)}}}function m(b,w){let y,v;return o.run(b,w,C),Yr("runSync","run",v),y;function C(B,z){ht(B),y=z,v=!0}}function h(b,w){if(o.freeze(),dt("process",o.Parser),gt("process",o.Compiler),!w)return new Promise(y);y(null,w);function y(v,C){let B=un(b);o.run(o.parse(B),B,(x,T,R)=>{if(x||!T||!R)z(x);else{let N=o.stringify(T,R);N==null||(vl(N)?R.value=N:R.result=N),z(x,R)}});function z(x,T){x||!T?C(x):v?v(T):w(null,T)}}}function k(b){let w;o.freeze(),dt("processSync",o.Parser),gt("processSync",o.Compiler);let y=un(b);return o.process(y,v),Yr("processSync","process",w),y;function v(C){w=!0,ht(C)}}}function Qr(e,n){return typeof e=="function"&&e.prototype&&(Tl(e.prototype)||n in e.prototype)}function Tl(e){let n;for(n in e)if(Gr.call(e,n))return!0;return!1}function dt(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `Parser`")}function gt(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `Compiler`")}function yt(e,n){if(n)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Xr(e){if(!ln(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Yr(e,n,t){if(!t)throw new Error("`"+e+"` finished async. Use `"+n+"` instead")}function un(e){return Ll(e)?e:new ze(e)}function Ll(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function vl(e){return typeof e=="string"||(0,Kr.default)(e)}var Dl={};function bt(e,n){let t=n||Dl,r=typeof t.includeImageAlt=="boolean"?t.includeImageAlt:!0,i=typeof t.includeHtml=="boolean"?t.includeHtml:!0;return ei(e,r,i)}function ei(e,n,t){if(zl(e)){if("value"in e)return e.type==="html"&&!t?"":e.value;if(n&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Jr(e.children,n,t)}return Array.isArray(e)?Jr(e,n,t):""}function Jr(e,n,t){let r=[],i=-1;for(;++i<e.length;)r[i]=ei(e[i],n,t);return r.join("")}function zl(e){return!!(e&&typeof e=="object")}function ne(e,n,t,r){let i=e.length,o=0,l;if(n<0?n=-n>i?0:i+n:n=n>i?i:n,t=t>0?t:0,r.length<1e4)l=Array.from(r),l.unshift(n,t),[].splice.apply(e,l);else for(t&&[].splice.apply(e,[n,t]);o<r.length;)l=r.slice(o,o+1e4),l.unshift(n,0),[].splice.apply(e,l),o+=1e4,n+=1e4}function ue(e,n){return e.length>0?(ne(e,e.length,0,n),e):n}var ni={}.hasOwnProperty;function ti(e){let n={},t=-1;for(;++t<e.length;)Rl(n,e[t]);return n}function Rl(e,n){let t;for(t in n){let i=(ni.call(e,t)?e[t]:void 0)||(e[t]={}),o=n[t],l;for(l in o){ni.call(i,l)||(i[l]=[]);let u=o[l];_l(i[l],Array.isArray(u)?u:u?[u]:[])}}}function _l(e,n){let t=-1,r=[];for(;++t<n.length;)(n[t].add==="after"?e:r).push(n[t]);ne(e,0,0,r)}var ri=/[!-/:-@[-`{-~\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/;var ce=Fe(/[A-Za-z]/),an=Fe(/\d/),ii=Fe(/[\dA-Fa-f]/),G=Fe(/[\dA-Za-z]/),oi=Fe(/[!-/:-@[-`{-~]/),wt=Fe(/[#-'*+\--9=?A-Z^-~]/);function sn(e){return e!==null&&(e<32||e===127)}function Y(e){return e!==null&&(e<0||e===32)}function A(e){return e!==null&&e<-2}function j(e){return e===-2||e===-1||e===32}var li=Fe(/\s/),ui=Fe(ri);function Fe(e){return n;function n(t){return t!==null&&e.test(String.fromCharCode(t))}}function O(e,n,t,r){let i=r?r-1:Number.POSITIVE_INFINITY,o=0;return l;function l(a){return j(a)?(e.enter(t),u(a)):n(a)}function u(a){return j(a)&&o++<i?(e.consume(a),u):(e.exit(t),n(a))}}var ai={tokenize:Bl};function Bl(e){let n=e.attempt(this.parser.constructs.contentInitial,r,i),t;return n;function r(u){if(u===null){e.consume(u);return}return e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),O(e,n,"linePrefix")}function i(u){return e.enter("paragraph"),o(u)}function o(u){let a=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=a),t=a,l(u)}function l(u){if(u===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(u);return}return A(u)?(e.consume(u),e.exit("chunkText"),o):(e.consume(u),l)}}var ci={tokenize:Nl},si={tokenize:Ml};function Nl(e){let n=this,t=[],r=0,i,o,l;return u;function u(C){if(r<t.length){let B=t[r];return n.containerState=B[1],e.attempt(B[0].continuation,a,f)(C)}return f(C)}function a(C){if(r++,n.containerState._closeFlow){n.containerState._closeFlow=void 0,i&&v();let B=n.events.length,z=B,x;for(;z--;)if(n.events[z][0]==="exit"&&n.events[z][1].type==="chunkFlow"){x=n.events[z][1].end;break}y(r);let T=B;for(;T<n.events.length;)n.events[T][1].end=Object.assign({},x),T++;return ne(n.events,z+1,0,n.events.slice(B)),n.events.length=T,f(C)}return u(C)}function f(C){if(r===t.length){if(!i)return m(C);if(i.currentConstruct&&i.currentConstruct.concrete)return k(C);n.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return n.containerState={},e.check(si,s,d)(C)}function s(C){return i&&v(),y(r),m(C)}function d(C){return n.parser.lazy[n.now().line]=r!==t.length,l=n.now().offset,k(C)}function m(C){return n.containerState={},e.attempt(si,h,k)(C)}function h(C){return r++,t.push([n.currentConstruct,n.containerState]),m(C)}function k(C){if(C===null){i&&v(),y(0),e.consume(C);return}return i=i||n.parser.flow(n.now()),e.enter("chunkFlow",{contentType:"flow",previous:o,_tokenizer:i}),b(C)}function b(C){if(C===null){w(e.exit("chunkFlow"),!0),y(0),e.consume(C);return}return A(C)?(e.consume(C),w(e.exit("chunkFlow")),r=0,n.interrupt=void 0,u):(e.consume(C),b)}function w(C,B){let z=n.sliceStream(C);if(B&&z.push(null),C.previous=o,o&&(o.next=C),o=C,i.defineSkip(C.start),i.write(z),n.parser.lazy[C.start.line]){let x=i.events.length;for(;x--;)if(i.events[x][1].start.offset<l&&(!i.events[x][1].end||i.events[x][1].end.offset>l))return;let T=n.events.length,R=T,N,Z;for(;R--;)if(n.events[R][0]==="exit"&&n.events[R][1].type==="chunkFlow"){if(N){Z=n.events[R][1].end;break}N=!0}for(y(r),x=T;x<n.events.length;)n.events[x][1].end=Object.assign({},Z),x++;ne(n.events,R+1,0,n.events.slice(T)),n.events.length=x}}function y(C){let B=t.length;for(;B-- >C;){let z=t[B];n.containerState=z[1],z[0].exit.call(n,e)}t.length=C}function v(){i.write([null]),o=void 0,i=void 0,n.containerState._closeFlow=void 0}}function Ml(e,n,t){return O(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function St(e){if(e===null||Y(e)||li(e))return 1;if(ui(e))return 2}function Qe(e,n,t){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(n=o(n,t),r.push(o))}return n}var cn={name:"attention",tokenize:Ul,resolveAll:jl};function jl(e,n){let t=-1,r,i,o,l,u,a,f,s;for(;++t<e.length;)if(e[t][0]==="enter"&&e[t][1].type==="attentionSequence"&&e[t][1]._close){for(r=t;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&n.sliceSerialize(e[r][1]).charCodeAt(0)===n.sliceSerialize(e[t][1]).charCodeAt(0)){if((e[r][1]._close||e[t][1]._open)&&(e[t][1].end.offset-e[t][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[t][1].end.offset-e[t][1].start.offset)%3))continue;a=e[r][1].end.offset-e[r][1].start.offset>1&&e[t][1].end.offset-e[t][1].start.offset>1?2:1;let d=Object.assign({},e[r][1].end),m=Object.assign({},e[t][1].start);pi(d,-a),pi(m,a),l={type:a>1?"strongSequence":"emphasisSequence",start:d,end:Object.assign({},e[r][1].end)},u={type:a>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[t][1].start),end:m},o={type:a>1?"strongText":"emphasisText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[t][1].start)},i={type:a>1?"strong":"emphasis",start:Object.assign({},l.start),end:Object.assign({},u.end)},e[r][1].end=Object.assign({},l.start),e[t][1].start=Object.assign({},u.end),f=[],e[r][1].end.offset-e[r][1].start.offset&&(f=ue(f,[["enter",e[r][1],n],["exit",e[r][1],n]])),f=ue(f,[["enter",i,n],["enter",l,n],["exit",l,n],["enter",o,n]]),f=ue(f,Qe(n.parser.constructs.insideSpan.null,e.slice(r+1,t),n)),f=ue(f,[["exit",o,n],["enter",u,n],["exit",u,n],["exit",i,n]]),e[t][1].end.offset-e[t][1].start.offset?(s=2,f=ue(f,[["enter",e[t][1],n],["exit",e[t][1],n]])):s=0,ne(e,r-1,t-r+3,f),t=r+f.length-s-2;break}}for(t=-1;++t<e.length;)e[t][1].type==="attentionSequence"&&(e[t][1].type="data");return e}function Ul(e,n){let t=this.parser.constructs.attentionMarkers.null,r=this.previous,i=St(r),o;return l;function l(a){return e.enter("attentionSequence"),o=a,u(a)}function u(a){if(a===o)return e.consume(a),u;let f=e.exit("attentionSequence"),s=St(a),d=!s||s===2&&i||t.includes(a),m=!i||i===2&&s||t.includes(r);return f._open=!!(o===42?d:d&&(i||!m)),f._close=!!(o===42?m:m&&(s||!d)),n(a)}}function pi(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}var Et={name:"autolink",tokenize:Hl};function Hl(e,n,t){let r=1;return i;function i(k){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(k),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o}function o(k){return ce(k)?(e.consume(k),l):wt(k)?f(k):t(k)}function l(k){return k===43||k===45||k===46||G(k)?u(k):f(k)}function u(k){return k===58?(e.consume(k),a):(k===43||k===45||k===46||G(k))&&r++<32?(e.consume(k),u):f(k)}function a(k){return k===62?(e.exit("autolinkProtocol"),h(k)):k===null||k===32||k===60||sn(k)?t(k):(e.consume(k),a)}function f(k){return k===64?(e.consume(k),r=0,s):wt(k)?(e.consume(k),f):t(k)}function s(k){return G(k)?d(k):t(k)}function d(k){return k===46?(e.consume(k),r=0,s):k===62?(e.exit("autolinkProtocol").type="autolinkEmail",h(k)):m(k)}function m(k){return(k===45||G(k))&&r++<63?(e.consume(k),k===45?m:d):t(k)}function h(k){return e.enter("autolinkMarker"),e.consume(k),e.exit("autolinkMarker"),e.exit("autolink"),n}}var Oe={tokenize:ql,partial:!0};function ql(e,n,t){return O(e,r,"linePrefix");function r(i){return i===null||A(i)?n(i):t(i)}}var bn={name:"blockQuote",tokenize:Vl,continuation:{tokenize:$l},exit:Wl};function Vl(e,n,t){let r=this;return i;function i(l){if(l===62){let u=r.containerState;return u.open||(e.enter("blockQuote",{_container:!0}),u.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(l),e.exit("blockQuoteMarker"),o}return t(l)}function o(l){return j(l)?(e.enter("blockQuotePrefixWhitespace"),e.consume(l),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(l))}}function $l(e,n,t){return O(e,e.attempt(bn,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function Wl(e){e.exit("blockQuote")}var wn={name:"characterEscape",tokenize:Ql};function Ql(e,n,t){return r;function r(o){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(o),e.exit("escapeMarker"),i}function i(o){return oi(o)?(e.enter("characterEscapeValue"),e.consume(o),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(o)}}var fi=document.createElement("i");function Xe(e){let n="&"+e+";";fi.innerHTML=n;let t=fi.textContent;return t.charCodeAt(t.length-1)===59&&e!=="semi"||t===n?!1:t}var Sn={name:"characterReference",tokenize:Xl};function Xl(e,n,t){let r=this,i=0,o,l;return u;function u(d){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(d),e.exit("characterReferenceMarker"),a}function a(d){return d===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(d),e.exit("characterReferenceMarkerNumeric"),f):(e.enter("characterReferenceValue"),o=31,l=G,s(d))}function f(d){return d===88||d===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(d),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,l=ii,s):(e.enter("characterReferenceValue"),o=7,l=an,s(d))}function s(d){let m;return d===59&&i?(m=e.exit("characterReferenceValue"),l===G&&!Xe(r.sliceSerialize(m))?t(d):(e.enter("characterReferenceMarker"),e.consume(d),e.exit("characterReferenceMarker"),e.exit("characterReference"),n)):l(d)&&i++<o?(e.consume(d),s):t(d)}}var En={name:"codeFenced",tokenize:Yl,concrete:!0};function Yl(e,n,t){let r=this,i={tokenize:z,partial:!0},o={tokenize:B,partial:!0},l=this.events[this.events.length-1],u=l&&l[1].type==="linePrefix"?l[2].sliceSerialize(l[1],!0).length:0,a=0,f;return s;function s(x){return e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),f=x,d(x)}function d(x){return x===f?(e.consume(x),a++,d):(e.exit("codeFencedFenceSequence"),a<3?t(x):O(e,m,"whitespace")(x))}function m(x){return x===null||A(x)?w(x):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),h(x))}function h(x){return x===null||Y(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),O(e,k,"whitespace")(x)):x===96&&x===f?t(x):(e.consume(x),h)}function k(x){return x===null||A(x)?w(x):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),b(x))}function b(x){return x===null||A(x)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),w(x)):x===96&&x===f?t(x):(e.consume(x),b)}function w(x){return e.exit("codeFencedFence"),r.interrupt?n(x):y(x)}function y(x){return x===null?C(x):A(x)?e.attempt(o,e.attempt(i,C,u?O(e,y,"linePrefix",u+1):y),C)(x):(e.enter("codeFlowValue"),v(x))}function v(x){return x===null||A(x)?(e.exit("codeFlowValue"),y(x)):(e.consume(x),v)}function C(x){return e.exit("codeFenced"),n(x)}function B(x,T,R){let N=this;return Z;function Z($){return x.enter("lineEnding"),x.consume($),x.exit("lineEnding"),D}function D($){return N.parser.lazy[N.now().line]?R($):T($)}}function z(x,T,R){let N=0;return O(x,Z,"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4);function Z(P){return x.enter("codeFencedFence"),x.enter("codeFencedFenceSequence"),D(P)}function D(P){return P===f?(x.consume(P),N++,D):N<a?R(P):(x.exit("codeFencedFenceSequence"),O(x,$,"whitespace")(P))}function $(P){return P===null||A(P)?(x.exit("codeFencedFence"),T(P)):R(P)}}}var pn={name:"codeIndented",tokenize:Gl},Kl={tokenize:Zl,partial:!0};function Gl(e,n,t){let r=this;return i;function i(f){return e.enter("codeIndented"),O(e,o,"linePrefix",4+1)(f)}function o(f){let s=r.events[r.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?l(f):t(f)}function l(f){return f===null?a(f):A(f)?e.attempt(Kl,l,a)(f):(e.enter("codeFlowValue"),u(f))}function u(f){return f===null||A(f)?(e.exit("codeFlowValue"),l(f)):(e.consume(f),u)}function a(f){return e.exit("codeIndented"),n(f)}}function Zl(e,n,t){let r=this;return i;function i(l){return r.parser.lazy[r.now().line]?t(l):A(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),i):O(e,o,"linePrefix",4+1)(l)}function o(l){let u=r.events[r.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?n(l):A(l)?i(l):t(l)}}var Ct={name:"codeText",tokenize:nu,resolve:Jl,previous:eu};function Jl(e){let n=e.length-4,t=3,r,i;if((e[t][1].type==="lineEnding"||e[t][1].type==="space")&&(e[n][1].type==="lineEnding"||e[n][1].type==="space")){for(r=t;++r<n;)if(e[r][1].type==="codeTextData"){e[t][1].type="codeTextPadding",e[n][1].type="codeTextPadding",t+=2,n-=2;break}}for(r=t-1,n++;++r<=n;)i===void 0?r!==n&&e[r][1].type!=="lineEnding"&&(i=r):(r===n||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),n-=r-i-2,r=i+2),i=void 0);return e}function eu(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function nu(e,n,t){let r=this,i=0,o,l;return u;function u(m){return e.enter("codeText"),e.enter("codeTextSequence"),a(m)}function a(m){return m===96?(e.consume(m),i++,a):(e.exit("codeTextSequence"),f(m))}function f(m){return m===null?t(m):m===96?(l=e.enter("codeTextSequence"),o=0,d(m)):m===32?(e.enter("space"),e.consume(m),e.exit("space"),f):A(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),f):(e.enter("codeTextData"),s(m))}function s(m){return m===null||m===32||m===96||A(m)?(e.exit("codeTextData"),f(m)):(e.consume(m),s)}function d(m){return m===96?(e.consume(m),o++,d):o===i?(e.exit("codeTextSequence"),e.exit("codeText"),n(m)):(l.type="codeTextData",s(m))}}function Cn(e){let n={},t=-1,r,i,o,l,u,a,f;for(;++t<e.length;){for(;t in n;)t=n[t];if(r=e[t],t&&r[1].type==="chunkFlow"&&e[t-1][1].type==="listItemPrefix"&&(a=r[1]._tokenizer.events,o=0,o<a.length&&a[o][1].type==="lineEndingBlank"&&(o+=2),o<a.length&&a[o][1].type==="content"))for(;++o<a.length&&a[o][1].type!=="content";)a[o][1].type==="chunkText"&&(a[o][1]._isInFirstContentOfListItem=!0,o++);if(r[0]==="enter")r[1].contentType&&(Object.assign(n,tu(e,t)),t=n[t],f=!0);else if(r[1]._container){for(o=t,i=void 0;o--&&(l=e[o],l[1].type==="lineEnding"||l[1].type==="lineEndingBlank");)l[0]==="enter"&&(i&&(e[i][1].type="lineEndingBlank"),l[1].type="lineEnding",i=o);i&&(r[1].end=Object.assign({},e[i][1].start),u=e.slice(i,t),u.unshift(r),ne(e,i,t-i+1,u))}}return!f}function tu(e,n){let t=e[n][1],r=e[n][2],i=n-1,o=[],l=t._tokenizer||r.parser[t.contentType](t.start),u=l.events,a=[],f={},s,d,m=-1,h=t,k=0,b=0,w=[b];for(;h;){for(;e[++i][1]!==h;);o.push(i),h._tokenizer||(s=r.sliceStream(h),h.next||s.push(null),d&&l.defineSkip(h.start),h._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(s),h._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),d=h,h=h.next}for(h=t;++m<u.length;)u[m][0]==="exit"&&u[m-1][0]==="enter"&&u[m][1].type===u[m-1][1].type&&u[m][1].start.line!==u[m][1].end.line&&(b=m+1,w.push(b),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(l.events=[],h?(h._tokenizer=void 0,h.previous=void 0):w.pop(),m=w.length;m--;){let y=u.slice(w[m],w[m+1]),v=o.pop();a.unshift([v,v+y.length-1]),ne(e,v,2,y)}for(m=-1;++m<a.length;)f[k+a[m][0]]=k+a[m][1],k+=a[m][1]-a[m][0]-1;return f}var At={tokenize:ou,resolve:iu},ru={tokenize:lu,partial:!0};function iu(e){return Cn(e),e}function ou(e,n){let t;return r;function r(u){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),i(u)}function i(u){return u===null?o(u):A(u)?e.check(ru,l,o)(u):(e.consume(u),i)}function o(u){return e.exit("chunkContent"),e.exit("content"),n(u)}function l(u){return e.consume(u),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,i}}function lu(e,n,t){let r=this;return i;function i(l){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),O(e,o,"linePrefix")}function o(l){if(l===null||A(l))return t(l);let u=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?n(l):e.interrupt(r.parser.constructs.flow,t,n)(l)}}function An(e,n,t,r,i,o,l,u,a){let f=a||Number.POSITIVE_INFINITY,s=0;return d;function d(y){return y===60?(e.enter(r),e.enter(i),e.enter(o),e.consume(y),e.exit(o),m):y===null||y===41||sn(y)?t(y):(e.enter(r),e.enter(l),e.enter(u),e.enter("chunkString",{contentType:"string"}),b(y))}function m(y){return y===62?(e.enter(o),e.consume(y),e.exit(o),e.exit(i),e.exit(r),n):(e.enter(u),e.enter("chunkString",{contentType:"string"}),h(y))}function h(y){return y===62?(e.exit("chunkString"),e.exit(u),m(y)):y===null||y===60||A(y)?t(y):(e.consume(y),y===92?k:h)}function k(y){return y===60||y===62||y===92?(e.consume(y),h):h(y)}function b(y){return y===40?++s>f?t(y):(e.consume(y),b):y===41?s--?(e.consume(y),b):(e.exit("chunkString"),e.exit(u),e.exit(l),e.exit(r),n(y)):y===null||Y(y)?s?t(y):(e.exit("chunkString"),e.exit(u),e.exit(l),e.exit(r),n(y)):sn(y)?t(y):(e.consume(y),y===92?w:b)}function w(y){return y===40||y===41||y===92?(e.consume(y),b):b(y)}}function Pn(e,n,t,r,i,o){let l=this,u=0,a;return f;function f(h){return e.enter(r),e.enter(i),e.consume(h),e.exit(i),e.enter(o),s}function s(h){return h===null||h===91||h===93&&!a||h===94&&!u&&"_hiddenFootnoteSupport"in l.parser.constructs||u>999?t(h):h===93?(e.exit(o),e.enter(i),e.consume(h),e.exit(i),e.exit(r),n):A(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),d(h))}function d(h){return h===null||h===91||h===93||A(h)||u++>999?(e.exit("chunkString"),s(h)):(e.consume(h),a=a||!j(h),h===92?m:d)}function m(h){return h===91||h===92||h===93?(e.consume(h),u++,d):d(h)}}function In(e,n,t,r,i,o){let l;return u;function u(m){return e.enter(r),e.enter(i),e.consume(m),e.exit(i),l=m===40?41:m,a}function a(m){return m===l?(e.enter(i),e.consume(m),e.exit(i),e.exit(r),n):(e.enter(o),f(m))}function f(m){return m===l?(e.exit(o),a(l)):m===null?t(m):A(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),O(e,f,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(m))}function s(m){return m===l||m===null||A(m)?(e.exit("chunkString"),f(m)):(e.consume(m),m===92?d:s)}function d(m){return m===l||m===92?(e.consume(m),s):s(m)}}function Re(e,n){let t;return r;function r(i){return A(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,r):j(i)?O(e,r,t?"linePrefix":"lineSuffix")(i):n(i)}}function Ee(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Pt={name:"definition",tokenize:au},uu={tokenize:su,partial:!0};function au(e,n,t){let r=this,i;return o;function o(a){return e.enter("definition"),Pn.call(r,e,l,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(a)}function l(a){return i=Ee(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),a===58?(e.enter("definitionMarker"),e.consume(a),e.exit("definitionMarker"),Re(e,An(e,e.attempt(uu,O(e,u,"whitespace"),O(e,u,"whitespace")),t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString"))):t(a)}function u(a){return a===null||A(a)?(e.exit("definition"),r.parser.defined.includes(i)||r.parser.defined.push(i),n(a)):t(a)}}function su(e,n,t){return r;function r(l){return Y(l)?Re(e,i)(l):t(l)}function i(l){return l===34||l===39||l===40?In(e,O(e,o,"whitespace"),t,"definitionTitle","definitionTitleMarker","definitionTitleString")(l):t(l)}function o(l){return l===null||A(l)?n(l):t(l)}}var It={name:"hardBreakEscape",tokenize:cu};function cu(e,n,t){return r;function r(o){return e.enter("hardBreakEscape"),e.enter("escapeMarker"),e.consume(o),i}function i(o){return A(o)?(e.exit("escapeMarker"),e.exit("hardBreakEscape"),n(o)):t(o)}}var Ft={name:"headingAtx",tokenize:fu,resolve:pu};function pu(e,n){let t=e.length-2,r=3,i,o;return e[r][1].type==="whitespace"&&(r+=2),t-2>r&&e[t][1].type==="whitespace"&&(t-=2),e[t][1].type==="atxHeadingSequence"&&(r===t-1||t-4>r&&e[t-2][1].type==="whitespace")&&(t-=r+1===t?2:4),t>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[t][1].end},o={type:"chunkText",start:e[r][1].start,end:e[t][1].end,contentType:"text"},ne(e,r,t-r+1,[["enter",i,n],["enter",o,n],["exit",o,n],["exit",i,n]])),e}function fu(e,n,t){let r=this,i=0;return o;function o(s){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),l(s)}function l(s){return s===35&&i++<6?(e.consume(s),l):s===null||Y(s)?(e.exit("atxHeadingSequence"),r.interrupt?n(s):u(s)):t(s)}function u(s){return s===35?(e.enter("atxHeadingSequence"),a(s)):s===null||A(s)?(e.exit("atxHeading"),n(s)):j(s)?O(e,u,"whitespace")(s):(e.enter("atxHeadingText"),f(s))}function a(s){return s===35?(e.consume(s),a):(e.exit("atxHeadingSequence"),u(s))}function f(s){return s===null||s===35||Y(s)?(e.exit("atxHeadingText"),u(s)):(e.consume(s),f)}}var mi=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ot=["pre","script","style","textarea"];var Tt={name:"htmlFlow",tokenize:du,resolveTo:hu,concrete:!0},mu={tokenize:gu,partial:!0};function hu(e){let n=e.length;for(;n--&&!(e[n][0]==="enter"&&e[n][1].type==="htmlFlow"););return n>1&&e[n-2][1].type==="linePrefix"&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2)),e}function du(e,n,t){let r=this,i,o,l,u,a;return f;function f(c){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(c),s}function s(c){return c===33?(e.consume(c),d):c===47?(e.consume(c),k):c===63?(e.consume(c),i=3,r.interrupt?n:oe):ce(c)?(e.consume(c),l=String.fromCharCode(c),o=!0,b):t(c)}function d(c){return c===45?(e.consume(c),i=2,m):c===91?(e.consume(c),i=5,l="CDATA[",u=0,h):ce(c)?(e.consume(c),i=4,r.interrupt?n:oe):t(c)}function m(c){return c===45?(e.consume(c),r.interrupt?n:oe):t(c)}function h(c){return c===l.charCodeAt(u++)?(e.consume(c),u===l.length?r.interrupt?n:D:h):t(c)}function k(c){return ce(c)?(e.consume(c),l=String.fromCharCode(c),b):t(c)}function b(c){return c===null||c===47||c===62||Y(c)?c!==47&&o&&Ot.includes(l.toLowerCase())?(i=1,r.interrupt?n(c):D(c)):mi.includes(l.toLowerCase())?(i=6,c===47?(e.consume(c),w):r.interrupt?n(c):D(c)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?t(c):o?v(c):y(c)):c===45||G(c)?(e.consume(c),l+=String.fromCharCode(c),b):t(c)}function w(c){return c===62?(e.consume(c),r.interrupt?n:D):t(c)}function y(c){return j(c)?(e.consume(c),y):N(c)}function v(c){return c===47?(e.consume(c),N):c===58||c===95||ce(c)?(e.consume(c),C):j(c)?(e.consume(c),v):N(c)}function C(c){return c===45||c===46||c===58||c===95||G(c)?(e.consume(c),C):B(c)}function B(c){return c===61?(e.consume(c),z):j(c)?(e.consume(c),B):v(c)}function z(c){return c===null||c===60||c===61||c===62||c===96?t(c):c===34||c===39?(e.consume(c),a=c,x):j(c)?(e.consume(c),z):(a=null,T(c))}function x(c){return c===null||A(c)?t(c):c===a?(e.consume(c),R):(e.consume(c),x)}function T(c){return c===null||c===34||c===39||c===60||c===61||c===62||c===96||Y(c)?B(c):(e.consume(c),T)}function R(c){return c===47||c===62||j(c)?v(c):t(c)}function N(c){return c===62?(e.consume(c),Z):t(c)}function Z(c){return j(c)?(e.consume(c),Z):c===null||A(c)?D(c):t(c)}function D(c){return c===45&&i===2?(e.consume(c),J):c===60&&i===1?(e.consume(c),ae):c===62&&i===4?(e.consume(c),X):c===63&&i===3?(e.consume(c),oe):c===93&&i===5?(e.consume(c),K):A(c)&&(i===6||i===7)?e.check(mu,X,$)(c):c===null||A(c)?$(c):(e.consume(c),D)}function $(c){return e.exit("htmlFlowData"),P(c)}function P(c){return c===null?p(c):A(c)?e.attempt({tokenize:M,partial:!0},P,p)(c):(e.enter("htmlFlowData"),D(c))}function M(c,Ge,ve){return He;function He(ke){return c.enter("lineEnding"),c.consume(ke),c.exit("lineEnding"),Ze}function Ze(ke){return r.parser.lazy[r.now().line]?ve(ke):Ge(ke)}}function J(c){return c===45?(e.consume(c),oe):D(c)}function ae(c){return c===47?(e.consume(c),l="",de):D(c)}function de(c){return c===62&&Ot.includes(l.toLowerCase())?(e.consume(c),X):ce(c)&&l.length<8?(e.consume(c),l+=String.fromCharCode(c),de):D(c)}function K(c){return c===93?(e.consume(c),oe):D(c)}function oe(c){return c===62?(e.consume(c),X):c===45&&i===2?(e.consume(c),oe):D(c)}function X(c){return c===null||A(c)?(e.exit("htmlFlowData"),p(c)):(e.consume(c),X)}function p(c){return e.exit("htmlFlow"),n(c)}}function gu(e,n,t){return r;function r(i){return e.exit("htmlFlowData"),e.enter("lineEndingBlank"),e.consume(i),e.exit("lineEndingBlank"),e.attempt(Oe,n,t)}}var Lt={name:"htmlText",tokenize:yu};function yu(e,n,t){let r=this,i,o,l,u;return a;function a(p){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(p),f}function f(p){return p===33?(e.consume(p),s):p===47?(e.consume(p),T):p===63?(e.consume(p),z):ce(p)?(e.consume(p),Z):t(p)}function s(p){return p===45?(e.consume(p),d):p===91?(e.consume(p),o="CDATA[",l=0,w):ce(p)?(e.consume(p),B):t(p)}function d(p){return p===45?(e.consume(p),m):t(p)}function m(p){return p===null||p===62?t(p):p===45?(e.consume(p),h):k(p)}function h(p){return p===null||p===62?t(p):k(p)}function k(p){return p===null?t(p):p===45?(e.consume(p),b):A(p)?(u=k,K(p)):(e.consume(p),k)}function b(p){return p===45?(e.consume(p),X):k(p)}function w(p){return p===o.charCodeAt(l++)?(e.consume(p),l===o.length?y:w):t(p)}function y(p){return p===null?t(p):p===93?(e.consume(p),v):A(p)?(u=y,K(p)):(e.consume(p),y)}function v(p){return p===93?(e.consume(p),C):y(p)}function C(p){return p===62?X(p):p===93?(e.consume(p),C):y(p)}function B(p){return p===null||p===62?X(p):A(p)?(u=B,K(p)):(e.consume(p),B)}function z(p){return p===null?t(p):p===63?(e.consume(p),x):A(p)?(u=z,K(p)):(e.consume(p),z)}function x(p){return p===62?X(p):z(p)}function T(p){return ce(p)?(e.consume(p),R):t(p)}function R(p){return p===45||G(p)?(e.consume(p),R):N(p)}function N(p){return A(p)?(u=N,K(p)):j(p)?(e.consume(p),N):X(p)}function Z(p){return p===45||G(p)?(e.consume(p),Z):p===47||p===62||Y(p)?D(p):t(p)}function D(p){return p===47?(e.consume(p),X):p===58||p===95||ce(p)?(e.consume(p),$):A(p)?(u=D,K(p)):j(p)?(e.consume(p),D):X(p)}function $(p){return p===45||p===46||p===58||p===95||G(p)?(e.consume(p),$):P(p)}function P(p){return p===61?(e.consume(p),M):A(p)?(u=P,K(p)):j(p)?(e.consume(p),P):D(p)}function M(p){return p===null||p===60||p===61||p===62||p===96?t(p):p===34||p===39?(e.consume(p),i=p,J):A(p)?(u=M,K(p)):j(p)?(e.consume(p),M):(e.consume(p),i=void 0,de)}function J(p){return p===i?(e.consume(p),ae):p===null?t(p):A(p)?(u=J,K(p)):(e.consume(p),J)}function ae(p){return p===62||p===47||Y(p)?D(p):t(p)}function de(p){return p===null||p===34||p===39||p===60||p===61||p===96?t(p):p===62||Y(p)?D(p):(e.consume(p),de)}function K(p){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),O(e,oe,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function oe(p){return e.enter("htmlTextData"),u(p)}function X(p){return p===62?(e.consume(p),e.exit("htmlTextData"),e.exit("htmlText"),n):t(p)}}var _e={name:"labelEnd",tokenize:Eu,resolveTo:Su,resolveAll:wu},xu={tokenize:Cu},ku={tokenize:Au},bu={tokenize:Pu};function wu(e){let n=-1,t;for(;++n<e.length;)t=e[n][1],(t.type==="labelImage"||t.type==="labelLink"||t.type==="labelEnd")&&(e.splice(n+1,t.type==="labelImage"?4:2),t.type="data",n++);return e}function Su(e,n){let t=e.length,r=0,i,o,l,u;for(;t--;)if(i=e[t][1],o){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[t][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(l){if(e[t][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(o=t,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(l=t);let a={type:e[o][1].type==="labelLink"?"link":"image",start:Object.assign({},e[o][1].start),end:Object.assign({},e[e.length-1][1].end)},f={type:"label",start:Object.assign({},e[o][1].start),end:Object.assign({},e[l][1].end)},s={type:"labelText",start:Object.assign({},e[o+r+2][1].end),end:Object.assign({},e[l-2][1].start)};return u=[["enter",a,n],["enter",f,n]],u=ue(u,e.slice(o+1,o+r+3)),u=ue(u,[["enter",s,n]]),u=ue(u,Qe(n.parser.constructs.insideSpan.null,e.slice(o+r+4,l-3),n)),u=ue(u,[["exit",s,n],e[l-2],e[l-1],["exit",f,n]]),u=ue(u,e.slice(l+1)),u=ue(u,[["exit",a,n]]),ne(e,o,e.length,u),e}function Eu(e,n,t){let r=this,i=r.events.length,o,l;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){o=r.events[i][1];break}return u;function u(s){return o?o._inactive?f(s):(l=r.parser.defined.includes(Ee(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelEnd"),a):t(s)}function a(s){return s===40?e.attempt(xu,n,l?n:f)(s):s===91?e.attempt(ku,n,l?e.attempt(bu,n,f):f)(s):l?n(s):f(s)}function f(s){return o._balanced=!0,t(s)}}function Cu(e,n,t){return r;function r(a){return e.enter("resource"),e.enter("resourceMarker"),e.consume(a),e.exit("resourceMarker"),Re(e,i)}function i(a){return a===41?u(a):An(e,o,t,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(a)}function o(a){return Y(a)?Re(e,l)(a):u(a)}function l(a){return a===34||a===39||a===40?In(e,Re(e,u),t,"resourceTitle","resourceTitleMarker","resourceTitleString")(a):u(a)}function u(a){return a===41?(e.enter("resourceMarker"),e.consume(a),e.exit("resourceMarker"),e.exit("resource"),n):t(a)}}function Au(e,n,t){let r=this;return i;function i(l){return Pn.call(r,e,o,t,"reference","referenceMarker","referenceString")(l)}function o(l){return r.parser.defined.includes(Ee(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(l):t(l)}}function Pu(e,n,t){return r;function r(o){return e.enter("reference"),e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),i}function i(o){return o===93?(e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),e.exit("reference"),n):t(o)}}var vt={name:"labelStartImage",tokenize:Iu,resolveAll:_e.resolveAll};function Iu(e,n,t){let r=this;return i;function i(u){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(u),e.exit("labelImageMarker"),o}function o(u){return u===91?(e.enter("labelMarker"),e.consume(u),e.exit("labelMarker"),e.exit("labelImage"),l):t(u)}function l(u){return u===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(u):n(u)}}var Dt={name:"labelStartLink",tokenize:Fu,resolveAll:_e.resolveAll};function Fu(e,n,t){let r=this;return i;function i(l){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(l),e.exit("labelMarker"),e.exit("labelLink"),o}function o(l){return l===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(l):n(l)}}var fn={name:"lineEnding",tokenize:Ou};function Ou(e,n){return t;function t(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),O(e,n,"linePrefix")}}var Be={name:"thematicBreak",tokenize:Tu};function Tu(e,n,t){let r=0,i;return o;function o(a){return e.enter("thematicBreak"),i=a,l(a)}function l(a){return a===i?(e.enter("thematicBreakSequence"),u(a)):j(a)?O(e,l,"whitespace")(a):r<3||a!==null&&!A(a)?t(a):(e.exit("thematicBreak"),n(a))}function u(a){return a===i?(e.consume(a),r++,u):(e.exit("thematicBreakSequence"),l(a))}}var re={name:"list",tokenize:Du,continuation:{tokenize:zu},exit:_u},Lu={tokenize:Bu,partial:!0},vu={tokenize:Ru,partial:!0};function Du(e,n,t){let r=this,i=r.events[r.events.length-1],o=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,l=0;return u;function u(h){let k=r.containerState.type||(h===42||h===43||h===45?"listUnordered":"listOrdered");if(k==="listUnordered"?!r.containerState.marker||h===r.containerState.marker:an(h)){if(r.containerState.type||(r.containerState.type=k,e.enter(k,{_container:!0})),k==="listUnordered")return e.enter("listItemPrefix"),h===42||h===45?e.check(Be,t,f)(h):f(h);if(!r.interrupt||h===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),a(h)}return t(h)}function a(h){return an(h)&&++l<10?(e.consume(h),a):(!r.interrupt||l<2)&&(r.containerState.marker?h===r.containerState.marker:h===41||h===46)?(e.exit("listItemValue"),f(h)):t(h)}function f(h){return e.enter("listItemMarker"),e.consume(h),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||h,e.check(Oe,r.interrupt?t:s,e.attempt(Lu,m,d))}function s(h){return r.containerState.initialBlankLine=!0,o++,m(h)}function d(h){return j(h)?(e.enter("listItemPrefixWhitespace"),e.consume(h),e.exit("listItemPrefixWhitespace"),m):t(h)}function m(h){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(h)}}function zu(e,n,t){let r=this;return r.containerState._closeFlow=void 0,e.check(Oe,i,o);function i(u){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,O(e,n,"listItemIndent",r.containerState.size+1)(u)}function o(u){return r.containerState.furtherBlankLines||!j(u)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,l(u)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(vu,n,l)(u))}function l(u){return r.containerState._closeFlow=!0,r.interrupt=void 0,O(e,e.attempt(re,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}function Ru(e,n,t){let r=this;return O(e,i,"listItemIndent",r.containerState.size+1);function i(o){let l=r.events[r.events.length-1];return l&&l[1].type==="listItemIndent"&&l[2].sliceSerialize(l[1],!0).length===r.containerState.size?n(o):t(o)}}function _u(e){e.exit(this.containerState.type)}function Bu(e,n,t){let r=this;return O(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4+1);function i(o){let l=r.events[r.events.length-1];return!j(o)&&l&&l[1].type==="listItemPrefixWhitespace"?n(o):t(o)}}var Fn={name:"setextUnderline",tokenize:Mu,resolveTo:Nu};function Nu(e,n){let t=e.length,r,i,o;for(;t--;)if(e[t][0]==="enter"){if(e[t][1].type==="content"){r=t;break}e[t][1].type==="paragraph"&&(i=t)}else e[t][1].type==="content"&&e.splice(t,1),!o&&e[t][1].type==="definition"&&(o=t);let l={type:"setextHeading",start:Object.assign({},e[i][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[i][1].type="setextHeadingText",o?(e.splice(i,0,["enter",l,n]),e.splice(o+1,0,["exit",e[r][1],n]),e[r][1].end=Object.assign({},e[o][1].end)):e[r][1]=l,e.push(["exit",l,n]),e}function Mu(e,n,t){let r=this,i=r.events.length,o,l;for(;i--;)if(r.events[i][1].type!=="lineEnding"&&r.events[i][1].type!=="linePrefix"&&r.events[i][1].type!=="content"){l=r.events[i][1].type==="paragraph";break}return u;function u(s){return!r.parser.lazy[r.now().line]&&(r.interrupt||l)?(e.enter("setextHeadingLine"),e.enter("setextHeadingLineSequence"),o=s,a(s)):t(s)}function a(s){return s===o?(e.consume(s),a):(e.exit("setextHeadingLineSequence"),O(e,f,"lineSuffix")(s))}function f(s){return s===null||A(s)?(e.exit("setextHeadingLine"),n(s)):t(s)}}var hi={tokenize:ju};function ju(e){let n=this,t=e.attempt(Oe,r,e.attempt(this.parser.constructs.flowInitial,i,O(e,e.attempt(this.parser.constructs.flow,i,e.attempt(At,i)),"linePrefix")));return t;function r(o){if(o===null){e.consume(o);return}return e.enter("lineEndingBlank"),e.consume(o),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t}function i(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n.currentConstruct=void 0,t}}var di={resolveAll:ki()},gi=xi("string"),yi=xi("text");function xi(e){return{tokenize:n,resolveAll:ki(e==="text"?Uu:void 0)};function n(t){let r=this,i=this.parser.constructs[e],o=t.attempt(i,l,u);return l;function l(s){return f(s)?o(s):u(s)}function u(s){if(s===null){t.consume(s);return}return t.enter("data"),t.consume(s),a}function a(s){return f(s)?(t.exit("data"),o(s)):(t.consume(s),a)}function f(s){if(s===null)return!0;let d=i[s],m=-1;if(d)for(;++m<d.length;){let h=d[m];if(!h.previous||h.previous.call(r,r.previous))return!0}return!1}}}function ki(e){return n;function n(t,r){let i=-1,o;for(;++i<=t.length;)o===void 0?t[i]&&t[i][1].type==="data"&&(o=i,i++):(!t[i]||t[i][1].type!=="data")&&(i!==o+2&&(t[o][1].end=t[i-1][1].end,t.splice(o+2,i-o-2),i=o+2),o=void 0);return e?e(t,r):t}}function Uu(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||e[t][1].type==="lineEnding")&&e[t-1][1].type==="data"){let r=e[t-1][1],i=n.sliceStream(r),o=i.length,l=-1,u=0,a;for(;o--;){let f=i[o];if(typeof f=="string"){for(l=f.length;f.charCodeAt(l-1)===32;)u++,l--;if(l)break;l=-1}else if(f===-2)a=!0,u++;else if(f!==-1){o++;break}}if(u){let f={type:t===e.length||a||u<2?"lineSuffix":"hardBreakTrailing",start:{line:r.end.line,column:r.end.column-u,offset:r.end.offset-u,_index:r.start._index+o,_bufferIndex:o?l:r.start._bufferIndex+l},end:Object.assign({},r.end)};r.end=Object.assign({},f.start),r.start.offset===r.end.offset?Object.assign(r,f):(e.splice(t,0,["enter",f,n],["exit",f,n]),t+=2)}t++}return e}function bi(e,n,t){let r=Object.assign(t?Object.assign({},t):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1}),i={},o=[],l=[],u=[],a=!0,f={consume:B,enter:z,exit:x,attempt:N(T),check:N(R),interrupt:N(R,{interrupt:!0})},s={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:b,sliceSerialize:k,now:w,defineSkip:y,write:h},d=n.tokenize.call(s,f),m;return n.resolveAll&&o.push(n),s;function h(P){return l=ue(l,P),v(),l[l.length-1]!==null?[]:(Z(n,0),s.events=Qe(o,s.events,s),s.events)}function k(P,M){return qu(b(P),M)}function b(P){return Hu(l,P)}function w(){return Object.assign({},r)}function y(P){i[P.line]=P.column,$()}function v(){let P;for(;r._index<l.length;){let M=l[r._index];if(typeof M=="string")for(P=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===P&&r._bufferIndex<M.length;)C(M.charCodeAt(r._bufferIndex));else C(M)}}function C(P){a=void 0,m=P,d=d(P)}function B(P){A(P)?(r.line++,r.column=1,r.offset+=P===-3?2:1,$()):P!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++)),s.previous=P,a=!0}function z(P,M){let J=M||{};return J.type=P,J.start=w(),s.events.push(["enter",J,s]),u.push(J),J}function x(P){let M=u.pop();return M.end=w(),s.events.push(["exit",M,s]),M}function T(P,M){Z(P,M.from)}function R(P,M){M.restore()}function N(P,M){return J;function J(ae,de,K){let oe,X,p,c;return Array.isArray(ae)?ve(ae):"tokenize"in ae?ve([ae]):Ge(ae);function Ge(ee){return Je;function Je(be){let qe=be!==null&&ee[be],De=be!==null&&ee.null,et=[...Array.isArray(qe)?qe:qe?[qe]:[],...Array.isArray(De)?De:De?[De]:[]];return ve(et)(be)}}function ve(ee){return oe=ee,X=0,ee.length===0?K:He(ee[X])}function He(ee){return Je;function Je(be){return c=D(),p=ee,ee.partial||(s.currentConstruct=ee),ee.name&&s.parser.constructs.disable.null.includes(ee.name)?ke(be):ee.tokenize.call(M?Object.assign(Object.create(s),M):s,f,Ze,ke)(be)}}function Ze(ee){return a=!0,P(p,c),de}function ke(ee){return a=!0,c.restore(),++X<oe.length?He(oe[X]):K}}}function Z(P,M){P.resolveAll&&!o.includes(P)&&o.push(P),P.resolve&&ne(s.events,M,s.events.length-M,P.resolve(s.events.slice(M),s)),P.resolveTo&&(s.events=P.resolveTo(s.events,s))}function D(){let P=w(),M=s.previous,J=s.currentConstruct,ae=s.events.length,de=Array.from(u);return{restore:K,from:ae};function K(){r=P,s.previous=M,s.currentConstruct=J,s.events.length=ae,u=de,$()}}function $(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function Hu(e,n){let t=n.start._index,r=n.start._bufferIndex,i=n.end._index,o=n.end._bufferIndex,l;return t===i?l=[e[t].slice(r,o)]:(l=e.slice(t,i),r>-1&&(l[0]=l[0].slice(r)),o>0&&l.push(e[i].slice(0,o))),l}function qu(e,n){let t=-1,r=[],i;for(;++t<e.length;){let o=e[t],l;if(typeof o=="string")l=o;else switch(o){case-5:{l="\r";break}case-4:{l=`
`;break}case-3:{l=`\r
`;break}case-2:{l=n?" ":"	";break}case-1:{if(!n&&i)continue;l=" ";break}default:l=String.fromCharCode(o)}i=o===-2,r.push(l)}return r.join("")}var zt={};tt(zt,{attentionMarkers:()=>Gu,contentInitial:()=>$u,disable:()=>Zu,document:()=>Vu,flow:()=>Qu,flowInitial:()=>Wu,insideSpan:()=>Ku,string:()=>Xu,text:()=>Yu});var Vu={[42]:re,[43]:re,[45]:re,[48]:re,[49]:re,[50]:re,[51]:re,[52]:re,[53]:re,[54]:re,[55]:re,[56]:re,[57]:re,[62]:bn},$u={[91]:Pt},Wu={[-2]:pn,[-1]:pn,[32]:pn},Qu={[35]:Ft,[42]:Be,[45]:[Fn,Be],[60]:Tt,[61]:Fn,[95]:Be,[96]:En,[126]:En},Xu={[38]:Sn,[92]:wn},Yu={[-5]:fn,[-4]:fn,[-3]:fn,[33]:vt,[38]:Sn,[42]:cn,[60]:[Et,Lt],[91]:Dt,[92]:[It,wn],[93]:_e,[95]:cn,[96]:Ct},Ku={null:[cn,di]},Gu={null:[42,95]},Zu={null:[]};function wi(e={}){let n=ti([zt].concat(e.extensions||[])),t={defined:[],lazy:{},constructs:n,content:r(ai),document:r(ci),flow:r(hi),string:r(gi),text:r(yi)};return t;function r(i){return o;function o(l){return bi(t,i,l)}}}var Si=/[\0\t\n\r]/g;function Ei(){let e=1,n="",t=!0,r;return i;function i(o,l,u){let a=[],f,s,d,m,h;for(o=n+o.toString(l),d=0,n="",t&&(o.charCodeAt(0)===65279&&d++,t=void 0);d<o.length;){if(Si.lastIndex=d,f=Si.exec(o),m=f&&f.index!==void 0?f.index:o.length,h=o.charCodeAt(m),!f){n=o.slice(d);break}if(h===10&&d===m&&r)a.push(-3),r=void 0;else switch(r&&(a.push(-5),r=void 0),d<m&&(a.push(o.slice(d,m)),e+=m-d),h){case 0:{a.push(65533),e++;break}case 9:{for(s=Math.ceil(e/4)*4,a.push(-2);e++<s;)a.push(-1);break}case 10:{a.push(-4),e=1;break}default:r=!0,e=1}d=m+1}return u&&(r&&a.push(-5),n&&a.push(n),a.push(null)),a}}function Ci(e){for(;!Cn(e););return e}function On(e,n){let t=Number.parseInt(e,n);return t<9||t===11||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||(t&65535)===65535||(t&65535)===65534||t>1114111?"\uFFFD":String.fromCharCode(t)}var Ju=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Ai(e){return e.replace(Ju,ea)}function ea(e,n,t){if(n)return n;if(t.charCodeAt(0)===35){let i=t.charCodeAt(1),o=i===120||i===88;return On(t.slice(o?2:1),o?16:10)}return Xe(t)||e}var Ii={}.hasOwnProperty,Rt=function(e,n,t){return typeof n!="string"&&(t=n,n=void 0),na(t)(Ci(wi(t).document().write(Ei()(e,n,!0))))};function na(e){let n={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:u(cr),autolinkProtocol:D,autolinkEmail:D,atxHeading:u(ur),blockQuote:u(qe),characterEscape:D,characterReference:D,codeFenced:u(De),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:u(De,a),codeText:u(et,a),codeTextData:D,data:D,codeFlowValue:D,definition:u(qo),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:u(Vo),hardBreakEscape:u(ar),hardBreakTrailing:u(ar),htmlFlow:u(sr,a),htmlFlowData:D,htmlText:u(sr,a),htmlTextData:D,image:u($o),label:a,link:u(cr),listItem:u(Wo),listItemValue:k,listOrdered:u(pr,h),listUnordered:u(pr),paragraph:u(Qo),reference:He,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:u(ur),strong:u(Xo),thematicBreak:u(Ko)},exit:{atxHeading:s(),atxHeadingSequence:T,autolink:s(),autolinkEmail:be,autolinkProtocol:Je,blockQuote:s(),characterEscapeValue:$,characterReferenceMarkerHexadecimal:ke,characterReferenceMarkerNumeric:ke,characterReferenceValue:ee,codeFenced:s(v),codeFencedFence:y,codeFencedFenceInfo:b,codeFencedFenceMeta:w,codeFlowValue:$,codeIndented:s(C),codeText:s(de),codeTextData:$,data:$,definition:s(),definitionDestinationString:x,definitionLabelString:B,definitionTitleString:z,emphasis:s(),hardBreakEscape:s(M),hardBreakTrailing:s(M),htmlFlow:s(J),htmlFlowData:$,htmlText:s(ae),htmlTextData:$,image:s(oe),label:p,labelText:X,lineEnding:P,link:s(K),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:Ze,resourceDestinationString:c,resourceTitleString:Ge,resource:ve,setextHeading:s(Z),setextHeadingLineSequence:N,setextHeadingText:R,strong:s(),thematicBreak:s()}};Fi(n,(e||{}).mdastExtensions||[]);let t={};return r;function r(g){let E={type:"root",children:[]},I={stack:[E],tokenStack:[],config:n,enter:f,exit:d,buffer:a,resume:m,setData:o,getData:l},H=[],V=-1;for(;++V<g.length;)if(g[V][1].type==="listOrdered"||g[V][1].type==="listUnordered")if(g[V][0]==="enter")H.push(V);else{let ge=H.pop();V=i(g,ge,V)}for(V=-1;++V<g.length;){let ge=n[g[V][0]];Ii.call(ge,g[V][1].type)&&ge[g[V][1].type].call(Object.assign({sliceSerialize:g[V][2].sliceSerialize},I),g[V][1])}if(I.tokenStack.length>0){let ge=I.tokenStack[I.tokenStack.length-1];(ge[1]||Pi).call(I,void 0,ge[0])}for(E.position={start:Te(g.length>0?g[0][1].start:{line:1,column:1,offset:0}),end:Te(g.length>0?g[g.length-2][1].end:{line:1,column:1,offset:0})},V=-1;++V<n.transforms.length;)E=n.transforms[V](E)||E;return E}function i(g,E,I){let H=E-1,V=-1,ge=!1,Ae,we,en,nn;for(;++H<=I;){let Q=g[H];if(Q[1].type==="listUnordered"||Q[1].type==="listOrdered"||Q[1].type==="blockQuote"?(Q[0]==="enter"?V++:V--,nn=void 0):Q[1].type==="lineEndingBlank"?Q[0]==="enter"&&(Ae&&!nn&&!V&&!en&&(en=H),nn=void 0):Q[1].type==="linePrefix"||Q[1].type==="listItemValue"||Q[1].type==="listItemMarker"||Q[1].type==="listItemPrefix"||Q[1].type==="listItemPrefixWhitespace"||(nn=void 0),!V&&Q[0]==="enter"&&Q[1].type==="listItemPrefix"||V===-1&&Q[0]==="exit"&&(Q[1].type==="listUnordered"||Q[1].type==="listOrdered")){if(Ae){let nt=H;for(we=void 0;nt--;){let Se=g[nt];if(Se[1].type==="lineEnding"||Se[1].type==="lineEndingBlank"){if(Se[0]==="exit")continue;we&&(g[we][1].type="lineEndingBlank",ge=!0),Se[1].type="lineEnding",we=nt}else if(!(Se[1].type==="linePrefix"||Se[1].type==="blockQuotePrefix"||Se[1].type==="blockQuotePrefixWhitespace"||Se[1].type==="blockQuoteMarker"||Se[1].type==="listItemIndent"))break}en&&(!we||en<we)&&(Ae._spread=!0),Ae.end=Object.assign({},we?g[we][1].start:Q[1].end),g.splice(we||H,0,["exit",Ae,Q[2]]),H++,I++}Q[1].type==="listItemPrefix"&&(Ae={type:"listItem",_spread:!1,start:Object.assign({},Q[1].start)},g.splice(H,0,["enter",Ae,Q[2]]),H++,I++,en=void 0,nn=!0)}}return g[E][1]._spread=ge,I}function o(g,E){t[g]=E}function l(g){return t[g]}function u(g,E){return I;function I(H){f.call(this,g(H),H),E&&E.call(this,H)}}function a(){this.stack.push({type:"fragment",children:[]})}function f(g,E,I){return this.stack[this.stack.length-1].children.push(g),this.stack.push(g),this.tokenStack.push([E,I]),g.position={start:Te(E.start)},g}function s(g){return E;function E(I){g&&g.call(this,I),d.call(this,I)}}function d(g,E){let I=this.stack.pop(),H=this.tokenStack.pop();if(H)H[0].type!==g.type&&(E?E.call(this,g,H[0]):(H[1]||Pi).call(this,g,H[0]));else throw new Error("Cannot close `"+g.type+"` ("+Ie({start:g.start,end:g.end})+"): it\u2019s not open");return I.position.end=Te(g.end),I}function m(){return bt(this.stack.pop())}function h(){o("expectingFirstListItemValue",!0)}function k(g){if(l("expectingFirstListItemValue")){let E=this.stack[this.stack.length-2];E.start=Number.parseInt(this.sliceSerialize(g),10),o("expectingFirstListItemValue")}}function b(){let g=this.resume(),E=this.stack[this.stack.length-1];E.lang=g}function w(){let g=this.resume(),E=this.stack[this.stack.length-1];E.meta=g}function y(){l("flowCodeInside")||(this.buffer(),o("flowCodeInside",!0))}function v(){let g=this.resume(),E=this.stack[this.stack.length-1];E.value=g.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),o("flowCodeInside")}function C(){let g=this.resume(),E=this.stack[this.stack.length-1];E.value=g.replace(/(\r?\n|\r)$/g,"")}function B(g){let E=this.resume(),I=this.stack[this.stack.length-1];I.label=E,I.identifier=Ee(this.sliceSerialize(g)).toLowerCase()}function z(){let g=this.resume(),E=this.stack[this.stack.length-1];E.title=g}function x(){let g=this.resume(),E=this.stack[this.stack.length-1];E.url=g}function T(g){let E=this.stack[this.stack.length-1];if(!E.depth){let I=this.sliceSerialize(g).length;E.depth=I}}function R(){o("setextHeadingSlurpLineEnding",!0)}function N(g){let E=this.stack[this.stack.length-1];E.depth=this.sliceSerialize(g).charCodeAt(0)===61?1:2}function Z(){o("setextHeadingSlurpLineEnding")}function D(g){let E=this.stack[this.stack.length-1],I=E.children[E.children.length-1];(!I||I.type!=="text")&&(I=Yo(),I.position={start:Te(g.start)},E.children.push(I)),this.stack.push(I)}function $(g){let E=this.stack.pop();E.value+=this.sliceSerialize(g),E.position.end=Te(g.end)}function P(g){let E=this.stack[this.stack.length-1];if(l("atHardBreak")){let I=E.children[E.children.length-1];I.position.end=Te(g.end),o("atHardBreak");return}!l("setextHeadingSlurpLineEnding")&&n.canContainEols.includes(E.type)&&(D.call(this,g),$.call(this,g))}function M(){o("atHardBreak",!0)}function J(){let g=this.resume(),E=this.stack[this.stack.length-1];E.value=g}function ae(){let g=this.resume(),E=this.stack[this.stack.length-1];E.value=g}function de(){let g=this.resume(),E=this.stack[this.stack.length-1];E.value=g}function K(){let g=this.stack[this.stack.length-1];if(l("inReference")){let E=l("referenceType")||"shortcut";g.type+="Reference",g.referenceType=E,delete g.url,delete g.title}else delete g.identifier,delete g.label;o("referenceType")}function oe(){let g=this.stack[this.stack.length-1];if(l("inReference")){let E=l("referenceType")||"shortcut";g.type+="Reference",g.referenceType=E,delete g.url,delete g.title}else delete g.identifier,delete g.label;o("referenceType")}function X(g){let E=this.sliceSerialize(g),I=this.stack[this.stack.length-2];I.label=Ai(E),I.identifier=Ee(E).toLowerCase()}function p(){let g=this.stack[this.stack.length-1],E=this.resume(),I=this.stack[this.stack.length-1];if(o("inReference",!0),I.type==="link"){let H=g.children;I.children=H}else I.alt=E}function c(){let g=this.resume(),E=this.stack[this.stack.length-1];E.url=g}function Ge(){let g=this.resume(),E=this.stack[this.stack.length-1];E.title=g}function ve(){o("inReference")}function He(){o("referenceType","collapsed")}function Ze(g){let E=this.resume(),I=this.stack[this.stack.length-1];I.label=E,I.identifier=Ee(this.sliceSerialize(g)).toLowerCase(),o("referenceType","full")}function ke(g){o("characterReferenceType",g.type)}function ee(g){let E=this.sliceSerialize(g),I=l("characterReferenceType"),H;I?(H=On(E,I==="characterReferenceMarkerNumeric"?10:16),o("characterReferenceType")):H=Xe(E);let V=this.stack.pop();V.value+=H,V.position.end=Te(g.end)}function Je(g){$.call(this,g);let E=this.stack[this.stack.length-1];E.url=this.sliceSerialize(g)}function be(g){$.call(this,g);let E=this.stack[this.stack.length-1];E.url="mailto:"+this.sliceSerialize(g)}function qe(){return{type:"blockquote",children:[]}}function De(){return{type:"code",lang:null,meta:null,value:""}}function et(){return{type:"inlineCode",value:""}}function qo(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Vo(){return{type:"emphasis",children:[]}}function ur(){return{type:"heading",depth:void 0,children:[]}}function ar(){return{type:"break"}}function sr(){return{type:"html",value:""}}function $o(){return{type:"image",title:null,url:"",alt:null}}function cr(){return{type:"link",title:null,url:"",children:[]}}function pr(g){return{type:"list",ordered:g.type==="listOrdered",start:null,spread:g._spread,children:[]}}function Wo(g){return{type:"listItem",spread:g._spread,checked:null,children:[]}}function Qo(){return{type:"paragraph",children:[]}}function Xo(){return{type:"strong",children:[]}}function Yo(){return{type:"text",value:""}}function Ko(){return{type:"thematicBreak"}}}function Te(e){return{line:e.line,column:e.column,offset:e.offset}}function Fi(e,n){let t=-1;for(;++t<n.length;){let r=n[t];Array.isArray(r)?Fi(e,r):ta(e,r)}}function ta(e,n){let t;for(t in n)if(Ii.call(n,t)){if(t==="canContainEols"){let r=n[t];r&&e[t].push(...r)}else if(t==="transforms"){let r=n[t];r&&e[t].push(...r)}else if(t==="enter"||t==="exit"){let r=n[t];r&&Object.assign(e[t],r)}}}function Pi(e,n){throw e?new Error("Cannot close `"+e.type+"` ("+Ie({start:e.start,end:e.end})+"): a different token (`"+n.type+"`, "+Ie({start:n.start,end:n.end})+") is open"):new Error("Cannot close document, a token (`"+n.type+"`, "+Ie({start:n.start,end:n.end})+") is still open")}function _t(e){Object.assign(this,{Parser:t=>{let r=this.data("settings");return Rt(t,Object.assign({},r,e,{extensions:this.data("micromarkExtensions")||[],mdastExtensions:this.data("fromMarkdownExtensions")||[]}))}})}var Oi=_t;function Ti(e,n){let t={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(n),!0)};return e.patch(n,t),e.applyData(n,t)}function Li(e,n){let t={type:"element",tagName:"br",properties:{},children:[]};return e.patch(n,t),[e.applyData(n,t),{type:"text",value:`
`}]}function vi(e,n){let t=n.value?n.value+`
`:"",r=n.lang?n.lang.match(/^[^ \t]+(?=[ \t]|$)/):null,i={};r&&(i.className=["language-"+r]);let o={type:"element",tagName:"code",properties:i,children:[{type:"text",value:t}]};return n.meta&&(o.data={meta:n.meta}),e.patch(n,o),o=e.applyData(n,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(n,o),o}function Di(e,n){let t={type:"element",tagName:"del",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function zi(e,n){let t={type:"element",tagName:"em",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function fe(e){let n=[],t=-1,r=0,i=0;for(;++t<e.length;){let o=e.charCodeAt(t),l="";if(o===37&&G(e.charCodeAt(t+1))&&G(e.charCodeAt(t+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(l=String.fromCharCode(o));else if(o>55295&&o<57344){let u=e.charCodeAt(t+1);o<56320&&u>56319&&u<57344?(l=String.fromCharCode(o,u),i=1):l="\uFFFD"}else l=String.fromCharCode(o);l&&(n.push(e.slice(r,t),encodeURIComponent(l)),r=t+i+1,l=""),i&&(t+=i,i=0)}return n.join("")+e.slice(r)}function Tn(e,n){let t=String(n.identifier).toUpperCase(),r=fe(t.toLowerCase()),i=e.footnoteOrder.indexOf(t),o;i===-1?(e.footnoteOrder.push(t),e.footnoteCounts[t]=1,o=e.footnoteOrder.length):(e.footnoteCounts[t]++,o=i+1);let l=e.footnoteCounts[t],u={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fn-"+r,id:e.clobberPrefix+"fnref-"+r+(l>1?"-"+l:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(o)}]};e.patch(n,u);let a={type:"element",tagName:"sup",properties:{},children:[u]};return e.patch(n,a),e.applyData(n,a)}function Ri(e,n){let t=e.footnoteById,r=1;for(;r in t;)r++;let i=String(r);return t[i]={type:"footnoteDefinition",identifier:i,children:[{type:"paragraph",children:n.children}],position:n.position},Tn(e,{type:"footnoteReference",identifier:i,position:n.position})}function _i(e,n){let t={type:"element",tagName:"h"+n.depth,properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Bi(e,n){if(e.dangerous){let t={type:"raw",value:n.value};return e.patch(n,t),e.applyData(n,t)}return null}function Ln(e,n){let t=n.referenceType,r="]";if(t==="collapsed"?r+="[]":t==="full"&&(r+="["+(n.label||n.identifier)+"]"),n.type==="imageReference")return{type:"text",value:"!["+n.alt+r};let i=e.all(n),o=i[0];o&&o.type==="text"?o.value="["+o.value:i.unshift({type:"text",value:"["});let l=i[i.length-1];return l&&l.type==="text"?l.value+=r:i.push({type:"text",value:r}),i}function Ni(e,n){let t=e.definition(n.identifier);if(!t)return Ln(e,n);let r={src:fe(t.url||""),alt:n.alt};t.title!==null&&t.title!==void 0&&(r.title=t.title);let i={type:"element",tagName:"img",properties:r,children:[]};return e.patch(n,i),e.applyData(n,i)}function Mi(e,n){let t={src:fe(n.url)};n.alt!==null&&n.alt!==void 0&&(t.alt=n.alt),n.title!==null&&n.title!==void 0&&(t.title=n.title);let r={type:"element",tagName:"img",properties:t,children:[]};return e.patch(n,r),e.applyData(n,r)}function ji(e,n){let t={type:"text",value:n.value.replace(/\r?\n|\r/g," ")};e.patch(n,t);let r={type:"element",tagName:"code",properties:{},children:[t]};return e.patch(n,r),e.applyData(n,r)}function Ui(e,n){let t=e.definition(n.identifier);if(!t)return Ln(e,n);let r={href:fe(t.url||"")};t.title!==null&&t.title!==void 0&&(r.title=t.title);let i={type:"element",tagName:"a",properties:r,children:e.all(n)};return e.patch(n,i),e.applyData(n,i)}function Hi(e,n){let t={href:fe(n.url)};n.title!==null&&n.title!==void 0&&(t.title=n.title);let r={type:"element",tagName:"a",properties:t,children:e.all(n)};return e.patch(n,r),e.applyData(n,r)}function qi(e,n,t){let r=e.all(n),i=t?ra(t):Vi(n),o={},l=[];if(typeof n.checked=="boolean"){let s=r[0],d;s&&s.type==="element"&&s.tagName==="p"?d=s:(d={type:"element",tagName:"p",properties:{},children:[]},r.unshift(d)),d.children.length>0&&d.children.unshift({type:"text",value:" "}),d.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:n.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let u=-1;for(;++u<r.length;){let s=r[u];(i||u!==0||s.type!=="element"||s.tagName!=="p")&&l.push({type:"text",value:`
`}),s.type==="element"&&s.tagName==="p"&&!i?l.push(...s.children):l.push(s)}let a=r[r.length-1];a&&(i||a.type!=="element"||a.tagName!=="p")&&l.push({type:"text",value:`
`});let f={type:"element",tagName:"li",properties:o,children:l};return e.patch(n,f),e.applyData(n,f)}function ra(e){let n=!1;if(e.type==="list"){n=e.spread||!1;let t=e.children,r=-1;for(;!n&&++r<t.length;)n=Vi(t[r])}return n}function Vi(e){let n=e.spread;return n==null?e.children.length>1:n}function $i(e,n){let t={},r=e.all(n),i=-1;for(typeof n.start=="number"&&n.start!==1&&(t.start=n.start);++i<r.length;){let l=r[i];if(l.type==="element"&&l.tagName==="li"&&l.properties&&Array.isArray(l.properties.className)&&l.properties.className.includes("task-list-item")){t.className=["contains-task-list"];break}}let o={type:"element",tagName:n.ordered?"ol":"ul",properties:t,children:e.wrap(r,!0)};return e.patch(n,o),e.applyData(n,o)}function Wi(e,n){let t={type:"element",tagName:"p",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Qi(e,n){let t={type:"root",children:e.wrap(e.all(n))};return e.patch(n,t),e.applyData(n,t)}function Xi(e,n){let t={type:"element",tagName:"strong",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}var Ye=Yi("start"),Ke=Yi("end");function Bt(e){return{start:Ye(e),end:Ke(e)}}function Yi(e){return n;function n(t){let r=t&&t.position&&t.position[e]||{};return{line:r.line||null,column:r.column||null,offset:r.offset>-1?r.offset:null}}}function Ki(e,n){let t=e.all(n),r=t.shift(),i=[];if(r){let l={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(n.children[0],l),i.push(l)}if(t.length>0){let l={type:"element",tagName:"tbody",properties:{},children:e.wrap(t,!0)},u=Ye(n.children[1]),a=Ke(n.children[n.children.length-1]);u.line&&a.line&&(l.position={start:u,end:a}),i.push(l)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(n,o),e.applyData(n,o)}function Gi(e,n,t){let r=t?t.children:void 0,o=(r?r.indexOf(n):1)===0?"th":"td",l=t&&t.type==="table"?t.align:void 0,u=l?l.length:n.children.length,a=-1,f=[];for(;++a<u;){let d=n.children[a],m={},h=l?l[a]:void 0;h&&(m.align=h);let k={type:"element",tagName:o,properties:m,children:[]};d&&(k.children=e.all(d),e.patch(d,k),k=e.applyData(n,k)),f.push(k)}let s={type:"element",tagName:"tr",properties:{},children:e.wrap(f,!0)};return e.patch(n,s),e.applyData(n,s)}function Zi(e,n){let t={type:"element",tagName:"td",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function eo(e){let n=String(e),t=/\r?\n|\r/g,r=t.exec(n),i=0,o=[];for(;r;)o.push(Ji(n.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=t.exec(n);return o.push(Ji(n.slice(i),i>0,!1)),o.join("")}function Ji(e,n,t){let r=0,i=e.length;if(n){let o=e.codePointAt(r);for(;o===9||o===32;)r++,o=e.codePointAt(r)}if(t){let o=e.codePointAt(i-1);for(;o===9||o===32;)i--,o=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function no(e,n){let t={type:"text",value:eo(String(n.value))};return e.patch(n,t),e.applyData(n,t)}function to(e,n){let t={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(n,t),e.applyData(n,t)}var ro={blockquote:Ti,break:Li,code:vi,delete:Di,emphasis:zi,footnoteReference:Tn,footnote:Ri,heading:_i,html:Bi,imageReference:Ni,image:Mi,inlineCode:ji,linkReference:Ui,link:Hi,listItem:qi,list:$i,paragraph:Wi,root:Qi,strong:Xi,table:Ki,tableCell:Zi,tableRow:Gi,text:no,thematicBreak:to,toml:vn,yaml:vn,definition:vn,footnoteDefinition:vn};function vn(){return null}var Dn=function(e){if(e==null)return ua;if(typeof e=="string")return la(e);if(typeof e=="object")return Array.isArray(e)?ia(e):oa(e);if(typeof e=="function")return zn(e);throw new Error("Expected function, string, or object as test")};function ia(e){let n=[],t=-1;for(;++t<e.length;)n[t]=Dn(e[t]);return zn(r);function r(...i){let o=-1;for(;++o<n.length;)if(n[o].call(this,...i))return!0;return!1}}function oa(e){return zn(n);function n(t){let r;for(r in e)if(t[r]!==e[r])return!1;return!0}}function la(e){return zn(n);function n(t){return t&&t.type===e}}function zn(e){return n;function n(t,...r){return!!(t&&typeof t=="object"&&"type"in t&&e.call(this,t,...r))}}function ua(){return!0}var Nt=!0,Rn=!1,Mt="skip",jt=function(e,n,t,r){typeof n=="function"&&typeof t!="function"&&(r=t,t=n,n=null);let i=Dn(n),o=r?-1:1;l(e,void 0,[])();function l(u,a,f){let s=u&&typeof u=="object"?u:{};if(typeof s.type=="string"){let m=typeof s.tagName=="string"?s.tagName:typeof s.name=="string"?s.name:void 0;Object.defineProperty(d,"name",{value:"node ("+(u.type+(m?"<"+m+">":""))+")"})}return d;function d(){let m=[],h,k,b;if((!n||i(u,a,f[f.length-1]||null))&&(m=aa(t(u,f)),m[0]===Rn))return m;if(u.children&&m[0]!==Mt)for(k=(r?u.children.length:-1)+o,b=f.concat(u);k>-1&&k<u.children.length;){if(h=l(u.children[k],k,b)(),h[0]===Rn)return h;k=typeof h[1]=="number"?h[1]:k+o}return m}}};function aa(e){return Array.isArray(e)?e:typeof e=="number"?[Nt,e]:[e]}var Ne=function(e,n,t,r){typeof n=="function"&&typeof t!="function"&&(r=t,t=n,n=null),jt(e,n,i,r);function i(o,l){let u=l[l.length-1];return t(o,u?u.children.indexOf(o):null,u)}};function Ut(e){return!e||!e.position||!e.position.start||!e.position.start.line||!e.position.start.column||!e.position.end||!e.position.end.line||!e.position.end.column}var io={}.hasOwnProperty;function Ht(e){let n=Object.create(null);if(!e||!e.type)throw new Error("mdast-util-definitions expected node");return Ne(e,"definition",r=>{let i=oo(r.identifier);i&&!io.call(n,i)&&(n[i]=r)}),t;function t(r){let i=oo(r);return i&&io.call(n,i)?n[i]:null}}function oo(e){return String(e||"").toUpperCase()}var _n={}.hasOwnProperty;function lo(e,n){let t=n||{},r=t.allowDangerousHtml||!1,i={};return l.dangerous=r,l.clobberPrefix=t.clobberPrefix===void 0||t.clobberPrefix===null?"user-content-":t.clobberPrefix,l.footnoteLabel=t.footnoteLabel||"Footnotes",l.footnoteLabelTagName=t.footnoteLabelTagName||"h2",l.footnoteLabelProperties=t.footnoteLabelProperties||{className:["sr-only"]},l.footnoteBackLabel=t.footnoteBackLabel||"Back to content",l.unknownHandler=t.unknownHandler,l.passThrough=t.passThrough,l.handlers=pe(pe({},ro),t.handlers),l.definition=Ht(e),l.footnoteById=i,l.footnoteOrder=[],l.footnoteCounts={},l.patch=sa,l.applyData=ca,l.one=u,l.all=a,l.wrap=fa,l.augment=o,Ne(e,"footnoteDefinition",f=>{let s=String(f.identifier).toUpperCase();_n.call(i,s)||(i[s]=f)}),l;function o(f,s){if(f&&"data"in f&&f.data){let d=f.data;d.hName&&(s.type!=="element"&&(s={type:"element",tagName:"",properties:{},children:[]}),s.tagName=d.hName),s.type==="element"&&d.hProperties&&(s.properties=pe(pe({},s.properties),d.hProperties)),"children"in s&&s.children&&d.hChildren&&(s.children=d.hChildren)}if(f){let d="type"in f?f:{position:f};Ut(d)||(s.position={start:Ye(d),end:Ke(d)})}return s}function l(f,s,d,m){return Array.isArray(d)&&(m=d,d={}),o(f,{type:"element",tagName:s,properties:d||{},children:m||[]})}function u(f,s){return uo(l,f,s)}function a(f){return qt(l,f)}}function sa(e,n){e.position&&(n.position=Bt(e))}function ca(e,n){let t=n;if(e&&e.data){let r=e.data.hName,i=e.data.hChildren,o=e.data.hProperties;typeof r=="string"&&(t.type==="element"?t.tagName=r:t={type:"element",tagName:r,properties:{},children:[]}),t.type==="element"&&o&&(t.properties=pe(pe({},t.properties),o)),"children"in t&&t.children&&i!==null&&i!==void 0&&(t.children=i)}return t}function uo(e,n,t){let r=n&&n.type;if(!r)throw new Error("Expected node, got `"+n+"`");return _n.call(e.handlers,r)?e.handlers[r](e,n,t):e.passThrough&&e.passThrough.includes(r)?"children"in n?Ve(pe({},n),{children:qt(e,n)}):n:e.unknownHandler?e.unknownHandler(e,n,t):pa(e,n)}function qt(e,n){let t=[];if("children"in n){let r=n.children,i=-1;for(;++i<r.length;){let o=uo(e,r[i],n);if(o){if(i&&r[i-1].type==="break"&&(!Array.isArray(o)&&o.type==="text"&&(o.value=o.value.replace(/^\s+/,"")),!Array.isArray(o)&&o.type==="element")){let l=o.children[0];l&&l.type==="text"&&(l.value=l.value.replace(/^\s+/,""))}Array.isArray(o)?t.push(...o):t.push(o)}}}return t}function pa(e,n){let t=n.data||{},r="value"in n&&!(_n.call(t,"hProperties")||_n.call(t,"hChildren"))?{type:"text",value:n.value}:{type:"element",tagName:"div",properties:{},children:qt(e,n)};return e.patch(n,r),e.applyData(n,r)}function fa(e,n){let t=[],r=-1;for(n&&t.push({type:"text",value:`
`});++r<e.length;)r&&t.push({type:"text",value:`
`}),t.push(e[r]);return n&&e.length>0&&t.push({type:"text",value:`
`}),t}function ao(e){let n=[],t=-1;for(;++t<e.footnoteOrder.length;){let r=e.footnoteById[e.footnoteOrder[t]];if(!r)continue;let i=e.all(r),o=String(r.identifier).toUpperCase(),l=fe(o.toLowerCase()),u=0,a=[];for(;++u<=e.footnoteCounts[o];){let d={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fnref-"+l+(u>1?"-"+u:""),dataFootnoteBackref:!0,className:["data-footnote-backref"],ariaLabel:e.footnoteBackLabel},children:[{type:"text",value:"\u21A9"}]};u>1&&d.children.push({type:"element",tagName:"sup",children:[{type:"text",value:String(u)}]}),a.length>0&&a.push({type:"text",value:" "}),a.push(d)}let f=i[i.length-1];if(f&&f.type==="element"&&f.tagName==="p"){let d=f.children[f.children.length-1];d&&d.type==="text"?d.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...a)}else i.push(...a);let s={type:"element",tagName:"li",properties:{id:e.clobberPrefix+"fn-"+l},children:e.wrap(i,!0)};e.patch(r,s),n.push(s)}if(n.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:e.footnoteLabelTagName,properties:Ve(pe({},JSON.parse(JSON.stringify(e.footnoteLabelProperties))),{id:"footnote-label"}),children:[{type:"text",value:e.footnoteLabel}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(n,!0)},{type:"text",value:`
`}]}}function Bn(e,n){let t=lo(e,n),r=t.one(e,null),i=ao(t);return i&&r.children.push({type:"text",value:`
`},i),Array.isArray(r)?{type:"root",children:r}:r}var ma=function(e,n){return e&&"run"in e?ha(e,n):da(e||n)},Vt=ma;function ha(e,n){return(t,r,i)=>{e.run(Bn(t,n),r,o=>{i(o)})}}function da(e){return n=>Bn(n,e)}var F=Pe(yo(),1);var Ce=class{constructor(n,t,r){this.property=n,this.normal=t,r&&(this.space=r)}};Ce.prototype.property={};Ce.prototype.normal={};Ce.prototype.space=null;function $t(e,n){let t={},r={},i=-1;for(;++i<e.length;)Object.assign(t,e[i].property),Object.assign(r,e[i].normal);return new Ce(t,r,n)}function mn(e){return e.toLowerCase()}var ie=class{constructor(n,t){this.property=n,this.attribute=t}};ie.prototype.space=null;ie.prototype.boolean=!1;ie.prototype.booleanish=!1;ie.prototype.overloadedBoolean=!1;ie.prototype.number=!1;ie.prototype.commaSeparated=!1;ie.prototype.spaceSeparated=!1;ie.prototype.commaOrSpaceSeparated=!1;ie.prototype.mustUseProperty=!1;ie.prototype.defined=!1;var hn={};tt(hn,{boolean:()=>L,booleanish:()=>W,commaOrSpaceSeparated:()=>se,commaSeparated:()=>Le,number:()=>S,overloadedBoolean:()=>Wt,spaceSeparated:()=>q});var xa=0,L=Me(),W=Me(),Wt=Me(),S=Me(),q=Me(),Le=Me(),se=Me();function Me(){return dr(2,++xa)}var Qt=Object.keys(hn),je=class extends ie{constructor(n,t,r,i){let o=-1;if(super(n,t),xo(this,"space",i),typeof r=="number")for(;++o<Qt.length;){let l=Qt[o];xo(this,Qt[o],(r&hn[l])===hn[l])}}};je.prototype.defined=!0;function xo(e,n,t){t&&(e[n]=t)}var ka={}.hasOwnProperty;function me(e){let n={},t={},r;for(r in e.properties)if(ka.call(e.properties,r)){let i=e.properties[r],o=new je(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),n[r]=o,t[mn(r)]=r,t[mn(o.attribute)]=r}return new Ce(n,t,e.space)}var Xt=me({space:"xlink",transform(e,n){return"xlink:"+n.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}});var Yt=me({space:"xml",transform(e,n){return"xml:"+n.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function Nn(e,n){return n in e?e[n]:n}function Mn(e,n){return Nn(e,n.toLowerCase())}var Kt=me({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:Mn,properties:{xmlns:null,xmlnsXLink:null}});var Gt=me({transform(e,n){return n==="role"?n:"aria-"+n.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:W,ariaAutoComplete:null,ariaBusy:W,ariaChecked:W,ariaColCount:S,ariaColIndex:S,ariaColSpan:S,ariaControls:q,ariaCurrent:null,ariaDescribedBy:q,ariaDetails:null,ariaDisabled:W,ariaDropEffect:q,ariaErrorMessage:null,ariaExpanded:W,ariaFlowTo:q,ariaGrabbed:W,ariaHasPopup:null,ariaHidden:W,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:q,ariaLevel:S,ariaLive:null,ariaModal:W,ariaMultiLine:W,ariaMultiSelectable:W,ariaOrientation:null,ariaOwns:q,ariaPlaceholder:null,ariaPosInSet:S,ariaPressed:W,ariaReadOnly:W,ariaRelevant:null,ariaRequired:W,ariaRoleDescription:q,ariaRowCount:S,ariaRowIndex:S,ariaRowSpan:S,ariaSelected:W,ariaSetSize:S,ariaSort:null,ariaValueMax:S,ariaValueMin:S,ariaValueNow:S,ariaValueText:null,role:null}});var ko=me({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:Mn,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Le,acceptCharset:q,accessKey:q,action:null,allow:null,allowFullScreen:L,allowPaymentRequest:L,allowUserMedia:L,alt:null,as:null,async:L,autoCapitalize:null,autoComplete:q,autoFocus:L,autoPlay:L,capture:L,charSet:null,checked:L,cite:null,className:q,cols:S,colSpan:null,content:null,contentEditable:W,controls:L,controlsList:q,coords:S|Le,crossOrigin:null,data:null,dateTime:null,decoding:null,default:L,defer:L,dir:null,dirName:null,disabled:L,download:Wt,draggable:W,encType:null,enterKeyHint:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:L,formTarget:null,headers:q,height:S,hidden:L,high:S,href:null,hrefLang:null,htmlFor:q,httpEquiv:q,id:null,imageSizes:null,imageSrcSet:null,inputMode:null,integrity:null,is:null,isMap:L,itemId:null,itemProp:q,itemRef:q,itemScope:L,itemType:q,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:L,low:S,manifest:null,max:null,maxLength:S,media:null,method:null,min:null,minLength:S,multiple:L,muted:L,name:null,nonce:null,noModule:L,noValidate:L,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:L,optimum:S,pattern:null,ping:q,placeholder:null,playsInline:L,poster:null,preload:null,readOnly:L,referrerPolicy:null,rel:q,required:L,reversed:L,rows:S,rowSpan:S,sandbox:q,scope:null,scoped:L,seamless:L,selected:L,shape:null,size:S,sizes:null,slot:null,span:S,spellCheck:W,src:null,srcDoc:null,srcLang:null,srcSet:null,start:S,step:null,style:null,tabIndex:S,target:null,title:null,translate:null,type:null,typeMustMatch:L,useMap:null,value:W,width:S,wrap:null,align:null,aLink:null,archive:q,axis:null,background:null,bgColor:null,border:S,borderColor:null,bottomMargin:S,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:L,declare:L,event:null,face:null,frame:null,frameBorder:null,hSpace:S,leftMargin:S,link:null,longDesc:null,lowSrc:null,marginHeight:S,marginWidth:S,noResize:L,noHref:L,noShade:L,noWrap:L,object:null,profile:null,prompt:null,rev:null,rightMargin:S,rules:null,scheme:null,scrolling:W,standby:null,summary:null,text:null,topMargin:S,valueType:null,version:null,vAlign:null,vLink:null,vSpace:S,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:L,disableRemotePlayback:L,prefix:null,property:null,results:S,security:null,unselectable:null}});var bo=me({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:Nn,properties:{about:se,accentHeight:S,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:S,amplitude:S,arabicForm:null,ascent:S,attributeName:null,attributeType:null,azimuth:S,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:S,by:null,calcMode:null,capHeight:S,className:q,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:S,diffuseConstant:S,direction:null,display:null,dur:null,divisor:S,dominantBaseline:null,download:L,dx:null,dy:null,edgeMode:null,editable:null,elevation:S,enableBackground:null,end:null,event:null,exponent:S,externalResourcesRequired:null,fill:null,fillOpacity:S,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Le,g2:Le,glyphName:Le,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:S,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:S,horizOriginX:S,horizOriginY:S,id:null,ideographic:S,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:S,k:S,k1:S,k2:S,k3:S,k4:S,kernelMatrix:se,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:S,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:S,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:S,overlineThickness:S,paintOrder:null,panose1:null,path:null,pathLength:S,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:q,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:S,pointsAtY:S,pointsAtZ:S,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:se,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:se,rev:se,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:se,requiredFeatures:se,requiredFonts:se,requiredFormats:se,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:S,specularExponent:S,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:S,strikethroughThickness:S,string:null,stroke:null,strokeDashArray:se,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:S,strokeOpacity:S,strokeWidth:null,style:null,surfaceScale:S,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:se,tabIndex:S,tableValues:null,target:null,targetX:S,targetY:S,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:se,to:null,transform:null,u1:null,u2:null,underlinePosition:S,underlineThickness:S,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:S,values:null,vAlphabetic:S,vMathematical:S,vectorEffect:null,vHanging:S,vIdeographic:S,version:null,vertAdvY:S,vertOriginX:S,vertOriginY:S,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:S,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}});var ba=/^data[-\w.:]+$/i,wo=/-[a-z]/g,wa=/[A-Z]/g;function Zt(e,n){let t=mn(n),r=n,i=ie;if(t in e.normal)return e.property[e.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&ba.test(n)){if(n.charAt(4)==="-"){let o=n.slice(5).replace(wo,Ea);r="data"+o.charAt(0).toUpperCase()+o.slice(1)}else{let o=n.slice(4);if(!wo.test(o)){let l=o.replace(wa,Sa);l.charAt(0)!=="-"&&(l="-"+l),n="data"+l}}i=je}return new i(r,n)}function Sa(e){return"-"+e.toLowerCase()}function Ea(e){return e.charAt(1).toUpperCase()}var jn={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var So=$t([Yt,Xt,Kt,Gt,ko],"html"),Eo=$t([Yt,Xt,Kt,Gt,bo],"svg");function Jt(e){if(e.allowedElements&&e.disallowedElements)throw new TypeError("Only one of `allowedElements` and `disallowedElements` should be defined");if(e.allowedElements||e.disallowedElements||e.allowElement)return n=>{Ne(n,"element",(t,r,i)=>{let o=i,l;if(e.allowedElements?l=!e.allowedElements.includes(t.tagName):e.disallowedElements&&(l=e.disallowedElements.includes(t.tagName)),!l&&e.allowElement&&typeof r=="number"&&(l=!e.allowElement(t,r,o)),l&&typeof r=="number")return e.unwrapDisallowed&&t.children?o.children.splice(r,1,...t.children):o.children.splice(r,1),r})}}var Gn=Pe(at(),1),Uo=Pe(Io(),1);function Fo(e){let n=e&&typeof e=="object"&&e.type==="text"?e.value||"":e;return typeof n=="string"&&n.replace(/[ \t\n\f\r]/g,"")===""}function Oo(e){return e.join(" ").trim()}function To(e,n){let t=n||{};return(e[e.length-1]===""?[...e,""]:e).join((t.padRight?" ":"")+","+(t.padLeft===!1?"":" ")).trim()}var Mo=Pe(No(),1),jo=Mo.default;var ir={}.hasOwnProperty,Ba=new Set(["table","thead","tbody","tfoot","tr"]);function or(e,n){let t=[],r=-1,i;for(;++r<n.children.length;)i=n.children[r],i.type==="element"?t.push(Na(e,i,r,n)):i.type==="text"?(n.type!=="element"||!Ba.has(n.tagName)||!Fo(i))&&t.push(i.value):i.type==="raw"&&!e.options.skipHtml&&t.push(i.value);return t}function Na(e,n,t,r){let i=e.options,o=i.transformLinkUri===void 0?dn:i.transformLinkUri,l=e.schema,u=n.tagName,a={},f=l,s;if(l.space==="html"&&u==="svg"&&(f=Eo,e.schema=f),n.properties)for(s in n.properties)ir.call(n.properties,s)&&ja(a,s,n.properties[s],e);(u==="ol"||u==="ul")&&e.listDepth++;let d=or(e,n);(u==="ol"||u==="ul")&&e.listDepth--,e.schema=l;let m=n.position||{start:{line:null,column:null,offset:null},end:{line:null,column:null,offset:null}},h=i.components&&ir.call(i.components,u)?i.components[u]:u,k=typeof h=="string"||h===Gn.default.Fragment;if(!Uo.default.isValidElementType(h))throw new TypeError(`Component for name \`${u}\` not defined or is not renderable`);if(a.key=t,u==="a"&&i.linkTarget&&(a.target=typeof i.linkTarget=="function"?i.linkTarget(String(a.href||""),n.children,typeof a.title=="string"?a.title:null):i.linkTarget),u==="a"&&o&&(a.href=o(String(a.href||""),n.children,typeof a.title=="string"?a.title:null)),!k&&u==="code"&&r.type==="element"&&r.tagName!=="pre"&&(a.inline=!0),!k&&(u==="h1"||u==="h2"||u==="h3"||u==="h4"||u==="h5"||u==="h6")&&(a.level=Number.parseInt(u.charAt(1),10)),u==="img"&&i.transformImageUri&&(a.src=i.transformImageUri(String(a.src||""),String(a.alt||""),typeof a.title=="string"?a.title:null)),!k&&u==="li"&&r.type==="element"){let b=Ma(n);a.checked=b&&b.properties?!!b.properties.checked:null,a.index=rr(r,n),a.ordered=r.tagName==="ol"}return!k&&(u==="ol"||u==="ul")&&(a.ordered=u==="ol",a.depth=e.listDepth),(u==="td"||u==="th")&&(a.align&&(a.style||(a.style={}),a.style.textAlign=a.align,delete a.align),k||(a.isHeader=u==="th")),!k&&u==="tr"&&r.type==="element"&&(a.isHeader=r.tagName==="thead"),i.sourcePos&&(a["data-sourcepos"]=qa(m)),!k&&i.rawSourcePos&&(a.sourcePosition=n.position),!k&&i.includeElementIndex&&(a.index=rr(r,n),a.siblingCount=rr(r)),k||(a.node=n),d.length>0?Gn.default.createElement(h,a,d):Gn.default.createElement(h,a)}function Ma(e){let n=-1;for(;++n<e.children.length;){let t=e.children[n];if(t.type==="element"&&t.tagName==="input")return t}return null}function rr(e,n){let t=-1,r=0;for(;++t<e.children.length&&e.children[t]!==n;)e.children[t].type==="element"&&r++;return r}function ja(e,n,t,r){let i=Zt(r.schema,n),o=t;o==null||o!==o||(Array.isArray(o)&&(o=i.commaSeparated?To(o):Oo(o)),i.property==="style"&&typeof o=="string"&&(o=Ua(o)),i.space&&i.property?e[ir.call(jn,i.property)?jn[i.property]:i.property]=o:i.attribute&&(e[i.attribute]=o))}function Ua(e){let n={};try{jo(e,t)}catch(r){}return n;function t(r,i){let o=r.slice(0,4)==="-ms-"?`ms-${r.slice(4)}`:r;n[o.replace(/-([a-z])/g,Ha)]=i}}function Ha(e,n){return n.toUpperCase()}function qa(e){return[e.start.line,":",e.start.column,"-",e.end.line,":",e.end.column].map(String).join("")}var Ho={}.hasOwnProperty,Va="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Zn={plugins:{to:"remarkPlugins",id:"change-plugins-to-remarkplugins"},renderers:{to:"components",id:"change-renderers-to-components"},astPlugins:{id:"remove-buggy-html-in-markdown-parser"},allowDangerousHtml:{id:"remove-buggy-html-in-markdown-parser"},escapeHtml:{id:"remove-buggy-html-in-markdown-parser"},source:{to:"children",id:"change-source-to-children"},allowNode:{to:"allowElement",id:"replace-allownode-allowedtypes-and-disallowedtypes"},allowedTypes:{to:"allowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},disallowedTypes:{to:"disallowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},includeNodeIndex:{to:"includeElementIndex",id:"change-includenodeindex-to-includeelementindex"}};function lr(e){for(let o in Zn)if(Ho.call(Zn,o)&&Ho.call(e,o)){let l=Zn[o];console.warn(`[react-markdown] Warning: please ${l.to?`use \`${l.to}\` instead of`:"remove"} \`${o}\` (see <${Va}#${l.id}> for more info)`),delete Zn[o]}let n=kt().use(Oi).use(e.remarkPlugins||[]).use(Vt,Ve(pe({},e.remarkRehypeOptions),{allowDangerousHtml:!0})).use(e.rehypePlugins||[]).use(Jt,e),t=new ze;typeof e.children=="string"?t.value=e.children:e.children!==void 0&&e.children!==null&&console.warn(`[react-markdown] Warning: please pass a string as \`children\` (not: \`${e.children}\`)`);let r=n.runSync(n.parse(t),t);if(r.type!=="root")throw new TypeError("Expected a `root` node");let i=Jn.default.createElement(Jn.default.Fragment,{},or({options:e,schema:So,listDepth:0},r));return e.className&&(i=Jn.default.createElement("div",{className:e.className},i)),i}lr.propTypes={children:F.default.string,className:F.default.string,allowElement:F.default.func,allowedElements:F.default.arrayOf(F.default.string),disallowedElements:F.default.arrayOf(F.default.string),unwrapDisallowed:F.default.bool,remarkPlugins:F.default.arrayOf(F.default.oneOfType([F.default.object,F.default.func,F.default.arrayOf(F.default.oneOfType([F.default.bool,F.default.string,F.default.object,F.default.func,F.default.arrayOf(F.default.any)]))])),rehypePlugins:F.default.arrayOf(F.default.oneOfType([F.default.object,F.default.func,F.default.arrayOf(F.default.oneOfType([F.default.bool,F.default.string,F.default.object,F.default.func,F.default.arrayOf(F.default.any)]))])),sourcePos:F.default.bool,rawSourcePos:F.default.bool,skipHtml:F.default.bool,includeElementIndex:F.default.bool,transformLinkUri:F.default.oneOfType([F.default.func,F.default.bool]),linkTarget:F.default.oneOfType([F.default.func,F.default.string]),transformImageUri:F.default.func,components:F.default.object};return il($a);})();
/*! Bundled license information:

react/cjs/react.production.min.js:
  (**
   * @license React
   * react.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

is-buffer/index.js:
  (*!
   * Determine if an object is a Buffer
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)

react-is/cjs/react-is.production.min.js:
  (**
   * @license React
   * react-is.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
return ReactMarkdown;})));
